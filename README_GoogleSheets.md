# 🔗 Dashboard de Projetos FACEPE - Conexão Google Sheets

Sistema completo de dashboard web que conecta diretamente com planilhas do Google Sheets via link, sem necessidade de upload de arquivos.

## 🎯 Características Principais

- **🔗 Conexão Direta**: Liga diretamente com Google Sheets via URL
- **🌐 Interface Web**: Dashboard responsivo e moderno
- **📊 Gráficos Interativos**: Visualizações com Chart.js
- **📱 Mobile-First**: Totalmente responsivo
- **🔄 Processamento Automático**: Importa dados em tempo real
- **🎨 Design Moderno**: Interface glassmorphism com gradientes
- **✅ Validação de Link**: Testa conexão antes de importar

## 📁 Estrutura dos Arquivos

```
📂 Web App Google Sheets
├── 📄 WebAppGoogleSheets.gs    # Código backend Google Apps Script
├── 📄 IndexGoogleSheets.html   # Interface HTML principal
└── 📄 README_GoogleSheets.md   # Este arquivo de instruções
```

## 🚀 Instalação e Deploy

### Passo 1: Criar Projeto Google Apps Script

1. **Acesse [script.google.com](https://script.google.com)**
2. **Clique em "Novo projeto"**
3. **Renomeie para "Dashboard Projetos FACEPE"**

### Passo 2: Configurar Arquivos

1. **Substituir Code.gs**:
   - Cole o conteúdo de `WebAppGoogleSheets.gs`
   - Salve o arquivo

2. **Criar arquivo HTML**:
   - Clique em `+` → `Arquivo HTML`
   - Nomeie como `index`
   - Cole o conteúdo de `IndexGoogleSheets.html`
   - Salve o arquivo

### Passo 3: Deploy da Web App

1. **Fazer Deploy**:
   - Clique em `Implantar` → `Nova implantação`
   - Tipo: `Aplicativo da Web`
   - Executar como: `Eu`
   - Quem tem acesso: `Qualquer pessoa` (ou conforme necessário)

2. **Obter URL**:
   - Copie a URL da web app
   - Teste o acesso

## 🎛️ Como Usar

### Preparar Planilha Google Sheets

#### 1. **Criar/Abrir Planilha**
- Crie uma nova planilha no Google Sheets
- Ou abra uma planilha existente com dados de projetos

#### 2. **Estruturar Dados**
Sua planilha deve ter as seguintes colunas (primeira linha):

| Coluna | Descrição | Exemplo |
|--------|-----------|---------|
| ID | Identificador único | APQ-1386-21 |
| Título | Nome do projeto | DESENVOLVIMENTO TECNOLÓGICO |
| Responsável | Nome do responsável | João Silva |
| Status | Status atual | Em Andamento |
| Data_Início | Data de início | 15/01/2024 |
| Data_Fim | Data prevista | 01/12/2025 |
| Orçamento | Valor em reais | 150000 |
| Progresso | Percentual (0-100) | 75 |
| Categoria | Categoria do projeto | Tecnologia |
| Avaliador | Nome do avaliador | Maria Santos |

#### 3. **Compartilhar Planilha**
1. Clique em **"Compartilhar"** no canto superior direito
2. Em **"Acesso geral"**, selecione **"Qualquer pessoa com o link"**
3. Defina permissão como **"Visualizador"** ou **"Editor"**
4. Clique em **"Copiar link"**

### Usar a Web App

#### 1. **Acessar Interface**
- Abra a URL da web app no navegador
- Você verá a tela de conexão

#### 2. **Conectar Planilha**
1. **Cole o link** da planilha no campo de entrada
2. **Clique em "Testar Conexão"** para validar
3. **Verifique as informações** da planilha
4. **Clique em "Conectar Planilha"** para importar

#### 3. **Visualizar Dashboard**
- Dashboard é gerado automaticamente
- Gráficos são criados em tempo real
- Métricas são calculadas automaticamente

## 🔧 Funcionalidades da Interface

### **🔗 Área de Conexão:**
- **Campo de URL**: Validação em tempo real
- **Teste de Conexão**: Verifica acesso antes de importar
- **Informações da Planilha**: Mostra detalhes antes da conexão
- **Feedback Visual**: Cores indicam status da validação

### **📊 Dashboard Interativo:**
- **Cards de Métricas**: Total, orçamento, progresso médio
- **Status Coloridos**: Distribuição visual por status
- **Gráfico de Pizza**: Distribuição por status
- **Gráfico de Barras**: Projetos por categoria
- **Botão Nova Conexão**: Para trocar planilha

### **🎨 Design Responsivo:**
- **Desktop**: Layout em grid otimizado
- **Tablet**: Adaptação automática
- **Mobile**: Interface em coluna única
- **Animações**: Transições suaves

## 📋 Formatos de Link Aceitos

### **URLs Válidas:**
```
✅ https://docs.google.com/spreadsheets/d/1ABC123.../edit
✅ https://docs.google.com/spreadsheets/d/1ABC123...
✅ 1ABC123DEF456... (apenas o ID)
```

### **URLs Inválidas:**
```
❌ Links de outras plataformas
❌ Links sem permissão de acesso
❌ URLs malformadas
```

## 🔍 Validações Automáticas

### **Validação de Link:**
- ✅ Formato correto da URL
- ✅ ID válido da planilha
- ✅ Acesso permitido
- ✅ Planilha não vazia

### **Validação de Dados:**
- ✅ Presença de cabeçalhos
- ✅ Dados nas linhas seguintes
- ✅ Colunas reconhecidas automaticamente
- ✅ Tipos de dados válidos

## 🚨 Troubleshooting

### Problemas Comuns

#### 1. **"Não foi possível acessar a planilha"**
```
❌ Erro de acesso
```
**Soluções**:
- Verifique se a planilha está compartilhada
- Confirme permissões de "Qualquer pessoa com o link"
- Teste o link em uma aba anônima

#### 2. **"Link inválido"**
```
❌ Formato de URL incorreto
```
**Soluções**:
- Use o link completo do Google Sheets
- Copie diretamente do botão "Compartilhar"
- Verifique se não há caracteres extras

#### 3. **"Planilha está vazia"**
```
❌ Sem dados para processar
```
**Soluções**:
- Adicione cabeçalhos na primeira linha
- Insira dados nas linhas seguintes
- Verifique se está na aba correta

#### 4. **Gráficos não aparecem**
```
❌ Visualizações em branco
```
**Soluções**:
- Verifique conexão com internet (Chart.js CDN)
- Confirme se há dados válidos
- Recarregue a página

### **Logs e Debug:**
- **Apps Script Console**: Ver logs detalhados
- **Browser DevTools**: F12 → Console para erros
- **Network Tab**: Verificar carregamento de recursos

## 📊 Métricas Calculadas

### **KPIs Automáticos:**
- **Total de Projetos**: Contagem total
- **Orçamento Total**: Soma de todos os valores
- **Progresso Médio**: Média ponderada
- **Distribuição por Status**: Percentuais automáticos

### **Análises Visuais:**
- **Gráfico Pizza**: Distribuição de status com cores
- **Gráfico Barras**: Projetos por categoria
- **Cards Coloridos**: Status com cores específicas

## 🔒 Segurança e Privacidade

### **Acesso aos Dados:**
- ✅ **Apenas Leitura**: Web app só lê dados da planilha
- ✅ **Não Modifica**: Planilha original permanece intacta
- ✅ **Temporário**: Dados ficam apenas na sessão
- ✅ **HTTPS**: Conexão segura por padrão

### **Permissões Necessárias:**
- **Google Sheets API**: Para ler planilhas
- **Google Drive API**: Para acessar arquivos
- **Execução de Scripts**: Para processar dados

## 🎯 Vantagens desta Solução

### **✅ Benefícios:**
- **Sem Upload**: Conecta diretamente com Google Sheets
- **Tempo Real**: Dados sempre atualizados
- **Sem Armazenamento**: Não salva dados permanentemente
- **Flexível**: Aceita qualquer estrutura de planilha
- **Rápido**: Processamento instantâneo
- **Seguro**: Apenas leitura, sem modificações

### **🔄 Fluxo Simplificado:**
1. **Criar planilha** no Google Sheets
2. **Compartilhar** com link público
3. **Colar link** na web app
4. **Testar conexão** automaticamente
5. **Conectar e visualizar** dashboard

## 🚀 Próximos Passos

### **Melhorias Futuras:**
- [ ] **Múltiplas Planilhas**: Conectar várias fontes
- [ ] **Atualização Automática**: Refresh periódico
- [ ] **Filtros Avançados**: Filtros interativos
- [ ] **Exportação**: Download de relatórios
- [ ] **Histórico**: Versioning de conexões
- [ ] **Notificações**: Alertas de mudanças

---

## 🎉 **WEB APP PRONTA PARA USO!**

Sua aplicação web está configurada para conectar diretamente com planilhas Google Sheets e gerar dashboards interativos em tempo real!

**🔗 Próximo passo**: Faça o deploy, prepare sua planilha e comece a usar!
