<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Dashboard de Projetos FACEPE</title>
    
    <!-- CSS Styles -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .header h1 {
            color: #1f4e79;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 1.1rem;
        }
        
        .link-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 40px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .link-area {
            border: 3px dashed #1976d2;
            border-radius: 15px;
            padding: 40px 20px;
            background: #f8f9ff;
            transition: all 0.3s ease;
        }
        
        .link-area.connected {
            border-color: #4caf50;
            background: #f1f8e9;
        }
        
        .link-icon {
            font-size: 4rem;
            color: #1976d2;
            margin-bottom: 20px;
        }
        
        .link-text {
            font-size: 1.3rem;
            color: #1976d2;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        .link-hint {
            color: #666;
            font-size: 1rem;
            margin-bottom: 25px;
        }
        
        .input-container {
            margin: 20px 0;
            width: 100%;
        }
        
        .link-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #1976d2;
            border-radius: 10px;
            font-size: 16px;
            margin-bottom: 15px;
            transition: border-color 0.3s ease;
        }
        
        .link-input:focus {
            outline: none;
            border-color: #1565c0;
            box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
        }
        
        .link-input.valid {
            border-color: #4caf50;
        }
        
        .link-input.invalid {
            border-color: #f44336;
        }
        
        .btn-container {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            background: linear-gradient(45deg, #1976d2, #42a5f5);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(25, 118, 210, 0.3);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(25, 118, 210, 0.4);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .btn-success {
            background: linear-gradient(45deg, #4caf50, #81c784);
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }
        
        .btn-success:hover {
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }
        
        .info-conexao {
            display: none;
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            text-align: left;
        }
        
        .info-conexao h4 {
            color: #1976d2;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .progress-container {
            display: none;
            margin-top: 20px;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #4caf50, #81c784);
            width: 0%;
            transition: width 0.3s ease;
            animation: progress-animation 2s ease-in-out;
        }
        
        @keyframes progress-animation {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }
        
        .progress-text {
            margin-top: 10px;
            color: #666;
            font-size: 0.9rem;
            text-align: center;
        }
        
        .dashboard {
            display: none;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .card-icon {
            font-size: 2rem;
            margin-right: 15px;
        }
        
        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1f4e79;
        }
        
        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 5px;
        }
        
        .metric-label {
            color: #666;
            font-size: 1rem;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            margin: 8px 0;
            border-radius: 8px;
            font-weight: 500;
        }
        
        .chart-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .chart-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1f4e79;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 500;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .instructions {
            background: rgba(255,255,255,0.9);
            padding: 25px;
            border-radius: 15px;
            margin-top: 20px;
            text-align: left;
        }
        
        .instructions h3 {
            color: #1f4e79;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .instructions ol {
            color: #666;
            line-height: 1.8;
            padding-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
        }
        
        .tip {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            border-left: 4px solid #ffc107;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #1976d2;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .hidden {
            display: none !important;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .link-area {
                padding: 30px 15px;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .btn-container {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
    
    <!-- Chart.js Library -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>📊 Dashboard de Projetos FACEPE</h1>
            <p>Sistema de Acompanhamento e Análise de Projetos via Google Sheets</p>
        </div>
        
        <!-- Link Section -->
        <div id="linkSection" class="link-section">
            <div class="link-area" id="linkArea">
                <div class="link-icon">🔗</div>
                <div class="link-text">Conecte sua planilha Google Sheets</div>
                <div class="link-hint">Cole o link da sua planilha do Google Sheets<br>
                Certifique-se de que a planilha está compartilhada</div>
                
                <div class="input-container">
                    <input type="url" id="linkInput" class="link-input" 
                           placeholder="https://docs.google.com/spreadsheets/d/..." />
                    
                    <div class="btn-container">
                        <button class="btn" onclick="testarConexao()" id="btnTestar">
                            🔍 Testar Conexão
                        </button>
                        <button class="btn btn-success" onclick="conectarPlanilha()" id="btnConectar" disabled>
                            🔗 Conectar Planilha
                        </button>
                    </div>
                </div>
                
                <div id="infoConexao" class="info-conexao">
                    <h4>📋 Informações da Planilha</h4>
                    <div id="detalhesConexao"></div>
                </div>
            </div>
            
            <div class="progress-container" id="progressContainer">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text" id="progressText">Conectando à planilha...</div>
            </div>
            
            <div id="alertContainer"></div>
            
            <!-- Instruções -->
            <div class="instructions">
                <h3>📝 Como obter o link da planilha:</h3>
                <ol>
                    <li>Abra sua planilha no <strong>Google Sheets</strong></li>
                    <li>Clique em <strong>"Compartilhar"</strong> no canto superior direito</li>
                    <li>Em <strong>"Acesso geral"</strong>, selecione <strong>"Qualquer pessoa com o link"</strong></li>
                    <li>Defina permissão como <strong>"Visualizador"</strong> ou <strong>"Editor"</strong></li>
                    <li>Clique em <strong>"Copiar link"</strong></li>
                    <li>Cole o link no campo acima</li>
                </ol>
                
                <div class="tip">
                    <strong>💡 Dica:</strong> Sua planilha deve ter cabeçalhos na primeira linha e dados nas linhas seguintes.
                    <br><strong>Colunas esperadas:</strong> ID, Título, Responsável, Status, Data_Início, Data_Fim, Orçamento, Progresso, Categoria, Avaliador
                </div>
            </div>
        </div>

        <!-- Dashboard Section -->
        <div id="dashboardSection" class="dashboard">
            <!-- Metrics Cards -->
            <div class="dashboard-grid">
                <div class="card">
                    <div class="card-header">
                        <div class="card-icon">📊</div>
                        <div class="card-title">Total de Projetos</div>
                    </div>
                    <div class="metric-value" id="totalProjetos">0</div>
                    <div class="metric-label">projetos cadastrados</div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <div class="card-icon">💰</div>
                        <div class="card-title">Orçamento Total</div>
                    </div>
                    <div class="metric-value" id="orcamentoTotal">R$ 0</div>
                    <div class="metric-label">valor total investido</div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <div class="card-icon">📈</div>
                        <div class="card-title">Progresso Médio</div>
                    </div>
                    <div class="metric-value" id="progressoMedio">0%</div>
                    <div class="metric-label">progresso geral</div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <div class="card-icon">🎯</div>
                        <div class="card-title">Status dos Projetos</div>
                    </div>
                    <div id="statusContainer"></div>
                </div>
            </div>

            <!-- Charts -->
            <div class="chart-container">
                <div class="chart-title">📊 Distribuição por Status</div>
                <canvas id="statusChart" width="400" height="200"></canvas>
            </div>

            <div class="chart-container">
                <div class="chart-title">📂 Projetos por Categoria</div>
                <canvas id="categoriaChart" width="400" height="200"></canvas>
            </div>

            <!-- Botão para nova conexão -->
            <div style="text-align: center; margin-top: 30px;">
                <button class="btn" onclick="novaConexao()">
                    🔄 Conectar Nova Planilha
                </button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // Variáveis globais
        let dadosDashboard = [];
        let metricas = {};
        let graficos = {};
        let conexaoTestada = false;

        // Verificar se há dados ao carregar
        <? if (temDados) { ?>
            dadosDashboard = <?= JSON.stringify(dadosDashboard) ?>;
            metricas = <?= JSON.stringify(metricas) ?>;
            graficos = <?= JSON.stringify(graficos) ?>;

            document.getElementById('linkSection').style.display = 'none';
            document.getElementById('dashboardSection').style.display = 'block';

            // Carregar dashboard
            carregarDashboard();
        <? } ?>

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            const linkInput = document.getElementById('linkInput');

            // Validação em tempo real do link
            linkInput.addEventListener('input', function() {
                const link = linkInput.value.trim();

                if (link === '') {
                    linkInput.className = 'link-input';
                    conexaoTestada = false;
                    document.getElementById('btnConectar').disabled = true;
                    document.getElementById('infoConexao').style.display = 'none';
                    return;
                }

                // Validar formato do link
                const isValidLink = validarLinkGoogleSheets(link);

                if (isValidLink) {
                    linkInput.className = 'link-input valid';
                } else {
                    linkInput.className = 'link-input invalid';
                    conexaoTestada = false;
                    document.getElementById('btnConectar').disabled = true;
                    document.getElementById('infoConexao').style.display = 'none';
                }
            });

            // Enter para testar conexão
            linkInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    testarConexao();
                }
            });
        });

        // Validar formato do link Google Sheets
        function validarLinkGoogleSheets(link) {
            const padroes = [
                /https:\/\/docs\.google\.com\/spreadsheets\/d\/[a-zA-Z0-9-_]+/,
                /^[a-zA-Z0-9-_]+$/
            ];

            return padroes.some(padrao => padrao.test(link));
        }

        // Testar conexão com a planilha
        function testarConexao() {
            const linkInput = document.getElementById('linkInput');
            const link = linkInput.value.trim();

            if (!link) {
                mostrarAlerta('Por favor, insira o link da planilha', 'error');
                return;
            }

            if (!validarLinkGoogleSheets(link)) {
                mostrarAlerta('Link inválido. Use um link válido do Google Sheets', 'error');
                return;
            }

            // Mostrar loading
            const btnTestar = document.getElementById('btnTestar');
            btnTestar.disabled = true;
            btnTestar.innerHTML = '<span class="loading"></span> Testando...';

            // Testar conexão
            google.script.run
                .withSuccessHandler(function(resultado) {
                    btnTestar.disabled = false;
                    btnTestar.innerHTML = '🔍 Testar Conexão';

                    if (resultado.sucesso) {
                        mostrarAlerta('Conexão bem-sucedida!', 'success');
                        mostrarInfoConexao(resultado);
                        conexaoTestada = true;
                        document.getElementById('btnConectar').disabled = false;
                        linkInput.className = 'link-input valid';
                        document.getElementById('linkArea').classList.add('connected');
                    } else {
                        mostrarAlerta(resultado.mensagem, 'error');
                        conexaoTestada = false;
                        document.getElementById('btnConectar').disabled = true;
                        linkInput.className = 'link-input invalid';
                    }
                })
                .withFailureHandler(function(erro) {
                    btnTestar.disabled = false;
                    btnTestar.innerHTML = '🔍 Testar Conexão';
                    mostrarAlerta('Erro ao testar conexão: ' + erro.message, 'error');
                    conexaoTestada = false;
                    document.getElementById('btnConectar').disabled = true;
                })
                .testarConexaoPlanilha(link);
        }

        // Mostrar informações da conexão
        function mostrarInfoConexao(info) {
            const container = document.getElementById('detalhesConexao');
            const infoDiv = document.getElementById('infoConexao');

            container.innerHTML = `
                <div class="info-item">
                    <strong>Nome da Planilha:</strong>
                    <span>${info.nomePlanilha}</span>
                </div>
                <div class="info-item">
                    <strong>Aba:</strong>
                    <span>${info.nomeAba}</span>
                </div>
                <div class="info-item">
                    <strong>Total de Linhas:</strong>
                    <span>${info.totalLinhas} projetos</span>
                </div>
                <div class="info-item">
                    <strong>Colunas Encontradas:</strong>
                    <span>${info.colunas.length} colunas</span>
                </div>
            `;

            infoDiv.style.display = 'block';
        }

        // Conectar planilha
        function conectarPlanilha() {
            if (!conexaoTestada) {
                mostrarAlerta('Teste a conexão primeiro', 'error');
                return;
            }

            const link = document.getElementById('linkInput').value.trim();

            // Mostrar progresso
            document.getElementById('progressContainer').style.display = 'block';
            document.getElementById('progressFill').style.width = '100%';

            const btnConectar = document.getElementById('btnConectar');
            btnConectar.disabled = true;
            btnConectar.innerHTML = '<span class="loading"></span> Conectando...';

            // Processar planilha
            google.script.run
                .withSuccessHandler(function(resultado) {
                    document.getElementById('progressContainer').style.display = 'none';
                    btnConectar.disabled = false;
                    btnConectar.innerHTML = '🔗 Conectar Planilha';

                    if (resultado.sucesso) {
                        mostrarAlerta(resultado.mensagem, 'success');
                        setTimeout(() => {
                            location.reload();
                        }, 2000);
                    } else {
                        mostrarAlerta(resultado.mensagem, 'error');
                    }
                })
                .withFailureHandler(function(erro) {
                    document.getElementById('progressContainer').style.display = 'none';
                    btnConectar.disabled = false;
                    btnConectar.innerHTML = '🔗 Conectar Planilha';
                    mostrarAlerta('Erro ao conectar planilha: ' + erro.message, 'error');
                })
                .processarLinkGoogleSheets(link);
        }

        // Nova conexão
        function novaConexao() {
            if (confirm('Deseja conectar uma nova planilha? Os dados atuais serão substituídos.')) {
                location.reload();
            }
        }

        // Mostrar alerta
        function mostrarAlerta(mensagem, tipo) {
            const container = document.getElementById('alertContainer');
            const alerta = document.createElement('div');
            alerta.className = `alert alert-${tipo}`;
            alerta.textContent = mensagem;

            container.innerHTML = '';
            container.appendChild(alerta);

            // Remover após 5 segundos
            setTimeout(() => {
                alerta.remove();
            }, 5000);
        }

        // Carregar dashboard
        function carregarDashboard() {
            if (metricas.resumoGeral) {
                document.getElementById('totalProjetos').textContent = metricas.resumoGeral.totalProjetos;
                document.getElementById('orcamentoTotal').textContent = 'R$ ' + metricas.resumoGeral.orcamentoTotal.toLocaleString('pt-BR');
                document.getElementById('progressoMedio').textContent = metricas.resumoGeral.progressoMedio + '%';
            }

            // Carregar status
            if (metricas.statusDistribuicao) {
                carregarStatus();
            }

            // Carregar gráficos
            if (graficos.status) {
                criarGraficoStatus();
            }

            if (graficos.categorias) {
                criarGraficoCategorias();
            }
        }

        // Carregar status
        function carregarStatus() {
            const container = document.getElementById('statusContainer');
            container.innerHTML = '';

            Object.entries(metricas.statusDistribuicao).forEach(([status, info]) => {
                const item = document.createElement('div');
                item.className = 'status-item';
                item.style.backgroundColor = graficos.cores[status] || '#e0e0e0';
                item.style.color = '#ffffff';

                item.innerHTML = `
                    <span>${status}</span>
                    <span>${info.quantidade} (${info.percentual}%)</span>
                `;

                container.appendChild(item);
            });
        }

        // Criar gráfico de status
        function criarGraficoStatus() {
            const ctx = document.getElementById('statusChart').getContext('2d');

            const labels = Object.keys(graficos.status);
            const data = Object.values(graficos.status);
            const colors = labels.map(label => graficos.cores[label] || '#e0e0e0');

            new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: labels,
                    datasets: [{
                        data: data,
                        backgroundColor: colors,
                        borderWidth: 2,
                        borderColor: '#ffffff'
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // Criar gráfico de categorias
        function criarGraficoCategorias() {
            const ctx = document.getElementById('categoriaChart').getContext('2d');

            const labels = Object.keys(graficos.categorias);
            const data = Object.values(graficos.categorias);

            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Número de Projetos',
                        data: data,
                        backgroundColor: '#1976d2',
                        borderColor: '#1565c0',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
    </script>
</body>
</html>
