/**
 * MÓDULO DE DASHBOARD VISUAL
 * Funções para criar interface visual e gráficos
 */

// ==================== CRIAÇÃO DO DASHBOARD PRINCIPAL ====================
/**
 * Cria o dashboard visual principal
 */
function criarDashboardVisual() {
  try {
    console.log('🎨 Criando dashboard visual...');
    
    const planilha = SpreadsheetApp.getActiveSpreadsheet();
    
    // Criar/obter aba do dashboard
    let abaDashboard = planilha.getSheetByName(CONFIG.ABA_DASHBOARD);
    if (!abaDashboard) {
      abaDashboard = planilha.insertSheet(CONFIG.ABA_DASHBOARD);
    }
    
    // Limpar conteúdo existente
    abaDashboard.clear();
    
    // Configurar layout do dashboard
    configurarLayoutDashboard(abaDashboard);
    
    // Criar seções do dashboard
    criarSecaoResumo(abaDashboard);
    criarSecaoStatus(abaDashboard);
    criarSecaoOrcamento(abaDashboard);
    criarSecaoAvaliadores(abaDashboard);
    criarSecaoPrazosCriticos(abaDashboard);
    
    // Criar gráficos
    criarGraficos();
    
    // Aplicar formatação final
    aplicarFormatacaoFinal(abaDashboard);
    
    console.log('✅ Dashboard visual criado com sucesso');
    
  } catch (error) {
    console.error('❌ Erro na criação do dashboard:', error);
    throw error;
  }
}

// ==================== CONFIGURAÇÃO DE LAYOUT ====================
/**
 * Configura o layout básico do dashboard
 */
function configurarLayoutDashboard(aba) {
  // Definir larguras das colunas
  aba.setColumnWidth(1, 200);  // Coluna A - Labels
  aba.setColumnWidth(2, 150);  // Coluna B - Valores
  aba.setColumnWidth(3, 50);   // Coluna C - Espaçador
  aba.setColumnWidth(4, 200);  // Coluna D - Labels
  aba.setColumnWidth(5, 150);  // Coluna E - Valores
  aba.setColumnWidth(6, 50);   // Coluna F - Espaçador
  aba.setColumnWidth(7, 300);  // Coluna G - Gráficos
  aba.setColumnWidth(8, 300);  // Coluna H - Gráficos
  
  // Título principal
  aba.getRange('A1:H1').merge();
  aba.getRange('A1').setValue('📊 DASHBOARD DE PROJETOS FACEPE');
  aba.getRange('A1')
    .setFontSize(20)
    .setFontWeight('bold')
    .setHorizontalAlignment('center')
    .setBackground('#1f4e79')
    .setFontColor('#ffffff');
    
  // Data de atualização
  aba.getRange('A2:H2').merge();
  aba.getRange('A2').setValue(`Última atualização: ${new Date().toLocaleString('pt-BR')}`);
  aba.getRange('A2')
    .setFontSize(10)
    .setHorizontalAlignment('center')
    .setBackground('#f0f0f0');
}

// ==================== SEÇÕES DO DASHBOARD ====================
/**
 * Cria seção de resumo geral
 */
function criarSecaoResumo(aba) {
  const planilha = SpreadsheetApp.getActiveSpreadsheet();
  const abaMetricas = planilha.getSheetByName(CONFIG.ABA_METRICAS);
  
  if (!abaMetricas) return;
  
  // Título da seção
  aba.getRange('A4').setValue('📈 RESUMO GERAL');
  aba.getRange('A4:B4')
    .setFontWeight('bold')
    .setFontSize(14)
    .setBackground('#e6f3ff');
  
  // Buscar dados das métricas
  const dadosMetricas = abaMetricas.getDataRange().getValues();
  let totalProjetos = 0, orcamentoTotal = 0, progressoMedio = 0;
  
  // Encontrar valores nas métricas
  for (let i = 0; i < dadosMetricas.length; i++) {
    const linha = dadosMetricas[i];
    if (linha[0] === 'Total de Projetos') totalProjetos = linha[1];
    if (linha[0] === 'Orçamento Total') orcamentoTotal = linha[1];
    if (linha[0] === 'Progresso Médio') progressoMedio = linha[1];
  }
  
  // Exibir métricas
  const metricas = [
    ['Total de Projetos:', totalProjetos],
    ['Orçamento Total:', `R$ ${formatarNumero(orcamentoTotal)}`],
    ['Progresso Médio:', `${progressoMedio}%`]
  ];
  
  aba.getRange(5, 1, metricas.length, 2).setValues(metricas);
  
  // Formatação
  aba.getRange(5, 1, metricas.length, 1).setFontWeight('bold');
  aba.getRange(5, 2, metricas.length, 1)
    .setHorizontalAlignment('right')
    .setNumberFormat('#,##0');
}

/**
 * Cria seção de distribuição por status
 */
function criarSecaoStatus(aba) {
  // Título da seção
  aba.getRange('D4').setValue('🎯 STATUS DOS PROJETOS');
  aba.getRange('D4:E4')
    .setFontWeight('bold')
    .setFontSize(14)
    .setBackground('#e6f3ff');
  
  // Obter dados de status
  const planilha = SpreadsheetApp.getActiveSpreadsheet();
  const abaDados = planilha.getSheetByName(CONFIG.ABA_DADOS);
  
  if (!abaDados) return;
  
  const dados = abaDados.getDataRange().getValues();
  const statusCount = {};
  
  // Contar status (assumindo coluna 4 = Status)
  for (let i = 1; i < dados.length; i++) {
    const status = dados[i][3] || 'Não Definido';
    statusCount[status] = (statusCount[status] || 0) + 1;
  }
  
  // Exibir contagem de status
  let linha = 5;
  Object.keys(statusCount).forEach(status => {
    aba.getRange(linha, 4).setValue(status + ':');
    aba.getRange(linha, 5).setValue(statusCount[status]);
    
    // Aplicar cor baseada no status
    if (CONFIG.CORES[status]) {
      aba.getRange(linha, 4, 1, 2).setBackground(CONFIG.CORES[status]);
      aba.getRange(linha, 4, 1, 2).setFontColor('#ffffff');
    }
    
    linha++;
  });
  
  aba.getRange(5, 4, Object.keys(statusCount).length, 1).setFontWeight('bold');
  aba.getRange(5, 5, Object.keys(statusCount).length, 1).setHorizontalAlignment('right');
}

/**
 * Cria seção de análise de orçamento
 */
function criarSecaoOrcamento(aba) {
  // Título da seção
  aba.getRange('A9').setValue('💰 ANÁLISE DE ORÇAMENTO');
  aba.getRange('A9:B9')
    .setFontWeight('bold')
    .setFontSize(14)
    .setBackground('#e6f3ff');
  
  // Calcular estatísticas de orçamento
  const planilha = SpreadsheetApp.getActiveSpreadsheet();
  const abaDados = planilha.getSheetByName(CONFIG.ABA_DADOS);
  
  if (!abaDados) return;
  
  const dados = abaDados.getDataRange().getValues();
  const orcamentos = [];
  
  // Coletar orçamentos (assumindo coluna 7 = Orçamento)
  for (let i = 1; i < dados.length; i++) {
    const orcamento = parseFloat(dados[i][6]) || 0;
    if (orcamento > 0) orcamentos.push(orcamento);
  }
  
  if (orcamentos.length > 0) {
    const total = orcamentos.reduce((sum, o) => sum + o, 0);
    const medio = total / orcamentos.length;
    const maximo = Math.max(...orcamentos);
    const minimo = Math.min(...orcamentos);
    
    const estatisticas = [
      ['Orçamento Total:', `R$ ${formatarNumero(total)}`],
      ['Orçamento Médio:', `R$ ${formatarNumero(medio)}`],
      ['Maior Orçamento:', `R$ ${formatarNumero(maximo)}`],
      ['Menor Orçamento:', `R$ ${formatarNumero(minimo)}`]
    ];
    
    aba.getRange(10, 1, estatisticas.length, 2).setValues(estatisticas);
    aba.getRange(10, 1, estatisticas.length, 1).setFontWeight('bold');
    aba.getRange(10, 2, estatisticas.length, 1).setHorizontalAlignment('right');
  }
}

/**
 * Cria seção de performance dos avaliadores
 */
function criarSecaoAvaliadores(aba) {
  // Título da seção
  aba.getRange('D9').setValue('👥 AVALIADORES');
  aba.getRange('D9:E9')
    .setFontWeight('bold')
    .setFontSize(14)
    .setBackground('#e6f3ff');
  
  // Contar projetos por avaliador
  const planilha = SpreadsheetApp.getActiveSpreadsheet();
  const abaDados = planilha.getSheetByName(CONFIG.ABA_DADOS);
  
  if (!abaDados) return;
  
  const dados = abaDados.getDataRange().getValues();
  const avaliadoresCount = {};
  
  // Contar projetos por avaliador (assumindo coluna 10 = Avaliador)
  for (let i = 1; i < dados.length; i++) {
    const avaliador = dados[i][9] || 'Não Atribuído';
    avaliadoresCount[avaliador] = (avaliadoresCount[avaliador] || 0) + 1;
  }
  
  // Exibir top avaliadores
  const avaliadoresOrdenados = Object.entries(avaliadoresCount)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 5);
  
  let linha = 10;
  avaliadoresOrdenados.forEach(([avaliador, count]) => {
    aba.getRange(linha, 4).setValue(avaliador + ':');
    aba.getRange(linha, 5).setValue(count);
    linha++;
  });
  
  aba.getRange(10, 4, avaliadoresOrdenados.length, 1).setFontWeight('bold');
  aba.getRange(10, 5, avaliadoresOrdenados.length, 1).setHorizontalAlignment('right');
}

/**
 * Cria seção de prazos críticos
 */
function criarSecaoPrazosCriticos(aba) {
  // Título da seção
  aba.getRange('A15').setValue('⚠️ PRAZOS CRÍTICOS (< 30 dias)');
  aba.getRange('A15:H15')
    .setFontWeight('bold')
    .setFontSize(14)
    .setBackground('#ffebee');
  
  // Cabeçalho da tabela
  const cabecalho = ['ID', 'Título', 'Responsável', 'Data Fim', 'Dias Restantes', 'Progresso', 'Status'];
  aba.getRange(16, 1, 1, cabecalho.length).setValues([cabecalho]);
  aba.getRange(16, 1, 1, cabecalho.length)
    .setFontWeight('bold')
    .setBackground('#f5f5f5');
  
  // Identificar projetos com prazos críticos
  const planilha = SpreadsheetApp.getActiveSpreadsheet();
  const abaDados = planilha.getSheetByName(CONFIG.ABA_DADOS);
  
  if (!abaDados) return;
  
  const dados = abaDados.getDataRange().getValues();
  const hoje = new Date();
  const prazosCriticos = [];
  
  for (let i = 1; i < dados.length; i++) {
    const linha = dados[i];
    const status = linha[3];
    const dataFim = new Date(linha[5]);
    
    if (status !== 'Concluído' && dataFim && !isNaN(dataFim)) {
      const diasRestantes = Math.ceil((dataFim - hoje) / (1000 * 60 * 60 * 24));
      
      if (diasRestantes <= 30) {
        prazosCriticos.push([
          linha[0], // ID
          linha[1], // Título
          linha[2], // Responsável
          dataFim.toLocaleDateString('pt-BR'),
          diasRestantes,
          linha[7] + '%', // Progresso
          linha[3] // Status
        ]);
      }
    }
  }
  
  // Ordenar por dias restantes
  prazosCriticos.sort((a, b) => a[4] - b[4]);
  
  // Exibir projetos críticos
  if (prazosCriticos.length > 0) {
    aba.getRange(17, 1, prazosCriticos.length, cabecalho.length).setValues(prazosCriticos);
    
    // Formatação condicional para dias restantes
    const rangeDias = aba.getRange(17, 5, prazosCriticos.length, 1);
    
    // Vermelho para < 7 dias
    const regraCritica = SpreadsheetApp.newConditionalFormatRule()
      .whenNumberLessThan(7)
      .setBackground('#ffcdd2')
      .setRanges([rangeDias])
      .build();
    
    // Amarelo para 7-15 dias
    const regraAlerta = SpreadsheetApp.newConditionalFormatRule()
      .whenNumberBetween(7, 15)
      .setBackground('#fff9c4')
      .setRanges([rangeDias])
      .build();
    
    aba.setConditionalFormatRules([regraCritica, regraAlerta]);
  } else {
    aba.getRange('A17').setValue('✅ Nenhum projeto com prazo crítico');
    aba.getRange('A17').setFontColor('#4caf50');
  }
}

// ==================== FORMATAÇÃO FINAL ====================
/**
 * Aplica formatação final ao dashboard
 */
function aplicarFormatacaoFinal(aba) {
  // Ajustar altura das linhas
  aba.setRowHeight(1, 40);
  aba.setRowHeight(2, 25);
  
  // Bordas para seções
  const ranges = ['A4:B8', 'D4:E8', 'A9:B13', 'D9:E13', 'A15:H25'];
  ranges.forEach(range => {
    aba.getRange(range).setBorder(true, true, true, true, true, true);
  });
  
  // Congelar primeiras linhas
  aba.setFrozenRows(3);
}

// ==================== FUNÇÕES AUXILIARES ====================
/**
 * Formata números para exibição
 */
function formatarNumero(numero) {
  if (typeof numero !== 'number') return '0';
  
  return numero.toLocaleString('pt-BR', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  });
}
