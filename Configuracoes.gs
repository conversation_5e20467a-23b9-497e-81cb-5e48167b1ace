/**
 * MÓDULO DE CONFIGURAÇÕES E FUNÇÕES AUXILIARES
 * Configurações avançadas e funções de suporte
 */

// ==================== CONFIGURAÇÕES AVANÇADAS ====================
/**
 * Abre interface de configurações
 */
function abrirConfiguracoes() {
  const html = HtmlService.createHtmlOutput(`
    <style>
      body { font-family: Arial, sans-serif; padding: 20px; }
      .config-section { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
      .config-title { font-weight: bold; color: #1f4e79; margin-bottom: 10px; }
      input, select { width: 100%; padding: 5px; margin: 5px 0; }
      button { background: #1f4e79; color: white; padding: 10px 20px; border: none; border-radius: 3px; cursor: pointer; }
      button:hover { background: #2c5aa0; }
      .status { padding: 10px; margin: 10px 0; border-radius: 3px; }
      .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
      .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
    
    <h2>⚙️ Configurações do Dashboard</h2>
    
    <div class="config-section">
      <div class="config-title">📁 Arquivo de Dados</div>
      <p>Nome do arquivo no Google Drive:</p>
      <input type="text" id="nomeArquivo" value="data.txt" placeholder="Nome do arquivo">
      <button onclick="verificarArquivo()">🔍 Verificar Arquivo</button>
      <div id="statusArquivo"></div>
    </div>
    
    <div class="config-section">
      <div class="config-title">🎨 Cores dos Status</div>
      <p>Concluído:</p>
      <input type="color" id="corConcluido" value="#4CAF50">
      <p>Em Andamento:</p>
      <input type="color" id="corAndamento" value="#FF9800">
      <p>Atrasado:</p>
      <input type="color" id="corAtrasado" value="#F44336">
      <p>Cancelado:</p>
      <input type="color" id="corCancelado" value="#9E9E9E">
    </div>
    
    <div class="config-section">
      <div class="config-title">⏰ Atualização Automática</div>
      <p>Frequência de atualização:</p>
      <select id="frequenciaAtualizacao">
        <option value="diaria">Diária (8h)</option>
        <option value="semanal">Semanal (Segunda 8h)</option>
        <option value="mensal">Mensal (Dia 1, 8h)</option>
        <option value="manual">Apenas Manual</option>
      </select>
    </div>
    
    <div class="config-section">
      <div class="config-title">📧 Relatórios por Email</div>
      <p>Email para envio de relatórios:</p>
      <input type="email" id="emailRelatorio" placeholder="<EMAIL>">
      <p>Enviar relatório:</p>
      <select id="frequenciaEmail">
        <option value="nunca">Nunca</option>
        <option value="semanal">Semanalmente</option>
        <option value="mensal">Mensalmente</option>
      </select>
    </div>
    
    <div class="config-section">
      <div class="config-title">🚨 Alertas de Prazo</div>
      <p>Alertar quando restam (dias):</p>
      <input type="number" id="diasAlerta" value="30" min="1" max="365">
      <p>Alertar projetos com progresso menor que (%):</p>
      <input type="number" id="progressoAlerta" value="50" min="0" max="100">
    </div>
    
    <br>
    <button onclick="salvarConfiguracoes()">💾 Salvar Configurações</button>
    <button onclick="restaurarPadrao()">🔄 Restaurar Padrão</button>
    <button onclick="google.script.host.close()">❌ Fechar</button>
    
    <div id="statusSalvar"></div>
    
    <script>
      function verificarArquivo() {
        const nome = document.getElementById('nomeArquivo').value;
        const status = document.getElementById('statusArquivo');
        
        status.innerHTML = '🔍 Verificando...';
        
        google.script.run
          .withSuccessHandler(function(resultado) {
            if (resultado.encontrado) {
              status.innerHTML = '<div class="success">✅ Arquivo encontrado! Última modificação: ' + resultado.dataModificacao + '</div>';
            } else {
              status.innerHTML = '<div class="error">❌ Arquivo não encontrado. Verifique se está no Google Drive.</div>';
            }
          })
          .withFailureHandler(function(erro) {
            status.innerHTML = '<div class="error">❌ Erro: ' + erro.message + '</div>';
          })
          .verificarArquivoExiste(nome);
      }
      
      function salvarConfiguracoes() {
        const config = {
          nomeArquivo: document.getElementById('nomeArquivo').value,
          cores: {
            'Concluído': document.getElementById('corConcluido').value,
            'Em Andamento': document.getElementById('corAndamento').value,
            'Atrasado': document.getElementById('corAtrasado').value,
            'Cancelado': document.getElementById('corCancelado').value
          },
          frequenciaAtualizacao: document.getElementById('frequenciaAtualizacao').value,
          emailRelatorio: document.getElementById('emailRelatorio').value,
          frequenciaEmail: document.getElementById('frequenciaEmail').value,
          diasAlerta: parseInt(document.getElementById('diasAlerta').value),
          progressoAlerta: parseInt(document.getElementById('progressoAlerta').value)
        };
        
        const status = document.getElementById('statusSalvar');
        status.innerHTML = '💾 Salvando...';
        
        google.script.run
          .withSuccessHandler(function() {
            status.innerHTML = '<div class="success">✅ Configurações salvas com sucesso!</div>';
          })
          .withFailureHandler(function(erro) {
            status.innerHTML = '<div class="error">❌ Erro ao salvar: ' + erro.message + '</div>';
          })
          .salvarConfiguracoesDashboard(config);
      }
      
      function restaurarPadrao() {
        if (confirm('Tem certeza que deseja restaurar as configurações padrão?')) {
          document.getElementById('nomeArquivo').value = 'data.txt';
          document.getElementById('corConcluido').value = '#4CAF50';
          document.getElementById('corAndamento').value = '#FF9800';
          document.getElementById('corAtrasado').value = '#F44336';
          document.getElementById('corCancelado').value = '#9E9E9E';
          document.getElementById('frequenciaAtualizacao').value = 'diaria';
          document.getElementById('emailRelatorio').value = '';
          document.getElementById('frequenciaEmail').value = 'nunca';
          document.getElementById('diasAlerta').value = '30';
          document.getElementById('progressoAlerta').value = '50';
        }
      }
      
      // Carregar configurações existentes
      google.script.run
        .withSuccessHandler(function(config) {
          if (config) {
            document.getElementById('nomeArquivo').value = config.nomeArquivo || 'data.txt';
            if (config.cores) {
              document.getElementById('corConcluido').value = config.cores['Concluído'] || '#4CAF50';
              document.getElementById('corAndamento').value = config.cores['Em Andamento'] || '#FF9800';
              document.getElementById('corAtrasado').value = config.cores['Atrasado'] || '#F44336';
              document.getElementById('corCancelado').value = config.cores['Cancelado'] || '#9E9E9E';
            }
            document.getElementById('frequenciaAtualizacao').value = config.frequenciaAtualizacao || 'diaria';
            document.getElementById('emailRelatorio').value = config.emailRelatorio || '';
            document.getElementById('frequenciaEmail').value = config.frequenciaEmail || 'nunca';
            document.getElementById('diasAlerta').value = config.diasAlerta || 30;
            document.getElementById('progressoAlerta').value = config.progressoAlerta || 50;
          }
        })
        .carregarConfiguracoesDashboard();
    </script>
  `)
    .setWidth(500)
    .setHeight(700);
  
  SpreadsheetApp.getUi().showModalDialog(html, 'Configurações do Dashboard');
}

// ==================== FUNÇÕES DE CONFIGURAÇÃO ====================
/**
 * Verifica se arquivo existe no Drive
 */
function verificarArquivoExiste(nomeArquivo) {
  try {
    const arquivos = DriveApp.getFilesByName(nomeArquivo);
    
    if (arquivos.hasNext()) {
      const arquivo = arquivos.next();
      return {
        encontrado: true,
        dataModificacao: arquivo.getLastUpdated().toLocaleString('pt-BR')
      };
    } else {
      return { encontrado: false };
    }
  } catch (error) {
    throw new Error('Erro ao verificar arquivo: ' + error.message);
  }
}

/**
 * Salva configurações do dashboard
 */
function salvarConfiguracoesDashboard(config) {
  try {
    const planilha = SpreadsheetApp.getActiveSpreadsheet();
    const propriedades = PropertiesService.getDocumentProperties();
    
    // Salvar configurações
    propriedades.setProperty('DASHBOARD_CONFIG', JSON.stringify(config));
    
    // Atualizar configurações globais
    if (config.cores) {
      Object.assign(CONFIG.CORES, config.cores);
    }
    
    // Reconfigurar triggers se necessário
    if (config.frequenciaAtualizacao !== 'manual') {
      configurarTriggersPersonalizados(config.frequenciaAtualizacao);
    }
    
    console.log('✅ Configurações salvas');
    
  } catch (error) {
    throw new Error('Erro ao salvar configurações: ' + error.message);
  }
}

/**
 * Carrega configurações do dashboard
 */
function carregarConfiguracoesDashboard() {
  try {
    const propriedades = PropertiesService.getDocumentProperties();
    const configStr = propriedades.getProperty('DASHBOARD_CONFIG');
    
    if (configStr) {
      return JSON.parse(configStr);
    }
    
    return null;
  } catch (error) {
    console.error('Erro ao carregar configurações:', error);
    return null;
  }
}

/**
 * Configura triggers personalizados
 */
function configurarTriggersPersonalizados(frequencia) {
  // Remover triggers existentes
  const triggers = ScriptApp.getProjectTriggers();
  triggers.forEach(trigger => {
    if (trigger.getHandlerFunction() === 'atualizarDashboardAutomatico') {
      ScriptApp.deleteTrigger(trigger);
    }
  });
  
  // Criar novo trigger baseado na frequência
  let trigger;
  
  switch (frequencia) {
    case 'diaria':
      trigger = ScriptApp.newTrigger('atualizarDashboardAutomatico')
        .timeBased()
        .everyDays(1)
        .atHour(8);
      break;
      
    case 'semanal':
      trigger = ScriptApp.newTrigger('atualizarDashboardAutomatico')
        .timeBased()
        .onWeekDay(ScriptApp.WeekDay.MONDAY)
        .atHour(8);
      break;
      
    case 'mensal':
      trigger = ScriptApp.newTrigger('atualizarDashboardAutomatico')
        .timeBased()
        .onMonthDay(1)
        .atHour(8);
      break;
  }
  
  if (trigger) {
    trigger.create();
  }
}

// ==================== RELATÓRIOS POR EMAIL ====================
/**
 * Envia relatório por email
 */
function enviarRelatorio() {
  try {
    const config = carregarConfiguracoesDashboard();
    
    if (!config || !config.emailRelatorio) {
      throw new Error('Email não configurado');
    }
    
    // Gerar relatório
    const relatorio = gerarRelatorioTexto();
    
    // Enviar email
    MailApp.sendEmail({
      to: config.emailRelatorio,
      subject: `📊 Relatório Dashboard Projetos - ${new Date().toLocaleDateString('pt-BR')}`,
      body: relatorio,
      htmlBody: gerarRelatorioHTML()
    });
    
    console.log('✅ Relatório enviado por email');
    
  } catch (error) {
    console.error('❌ Erro no envio de relatório:', error);
    throw error;
  }
}

/**
 * Gera relatório em texto
 */
function gerarRelatorioTexto() {
  const planilha = SpreadsheetApp.getActiveSpreadsheet();
  const abaDados = planilha.getSheetByName(CONFIG.ABA_DADOS);
  
  if (!abaDados) return 'Dados não encontrados';
  
  const dados = abaDados.getDataRange().getValues();
  
  // Calcular estatísticas básicas
  const total = dados.length - 1;
  const statusCount = {};
  
  for (let i = 1; i < dados.length; i++) {
    const status = dados[i][3] || 'Não Definido';
    statusCount[status] = (statusCount[status] || 0) + 1;
  }
  
  let relatorio = `RELATÓRIO DASHBOARD PROJETOS
Data: ${new Date().toLocaleString('pt-BR')}

RESUMO GERAL:
- Total de Projetos: ${total}

DISTRIBUIÇÃO POR STATUS:
`;
  
  Object.entries(statusCount).forEach(([status, count]) => {
    const percentual = ((count / total) * 100).toFixed(1);
    relatorio += `- ${status}: ${count} (${percentual}%)\n`;
  });
  
  return relatorio;
}

/**
 * Gera relatório em HTML
 */
function gerarRelatorioHTML() {
  const relatorioTexto = gerarRelatorioTexto();
  
  return `
    <html>
      <body style="font-family: Arial, sans-serif;">
        <h2 style="color: #1f4e79;">📊 Relatório Dashboard Projetos</h2>
        <pre style="background: #f5f5f5; padding: 15px; border-radius: 5px;">${relatorioTexto}</pre>
        <p><em>Relatório gerado automaticamente pelo Dashboard de Projetos FACEPE</em></p>
      </body>
    </html>
  `;
}

// ==================== FUNÇÕES DE ESTRUTURA ====================
/**
 * Cria estrutura básica da planilha
 */
function criarEstruturaPlanilha() {
  const planilha = SpreadsheetApp.getActiveSpreadsheet();
  
  // Criar abas necessárias se não existirem
  const abasNecessarias = [
    CONFIG.ABA_DADOS,
    CONFIG.ABA_DASHBOARD,
    CONFIG.ABA_METRICAS,
    CONFIG.ABA_GRAFICOS
  ];
  
  abasNecessarias.forEach(nomeAba => {
    let aba = planilha.getSheetByName(nomeAba);
    if (!aba) {
      aba = planilha.insertSheet(nomeAba);
      console.log(`✅ Aba "${nomeAba}" criada`);
    }
  });
  
  // Remover aba padrão se existir e estiver vazia
  try {
    const abaPadrao = planilha.getSheetByName('Planilha1');
    if (abaPadrao && abaPadrao.getLastRow() <= 1) {
      planilha.deleteSheet(abaPadrao);
    }
  } catch (error) {
    // Ignorar erro se aba não existir
  }
}

// ==================== FUNÇÕES DE BACKUP ====================
/**
 * Cria backup dos dados
 */
function criarBackup() {
  try {
    const planilha = SpreadsheetApp.getActiveSpreadsheet();
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const nomeBackup = `Backup_Dashboard_${timestamp}`;
    
    // Criar cópia da planilha
    const backup = planilha.copy(nomeBackup);
    
    // Mover para pasta de backups (criar se não existir)
    let pastaBackup;
    const pastas = DriveApp.getFoldersByName('Dashboard_Backups');
    
    if (pastas.hasNext()) {
      pastaBackup = pastas.next();
    } else {
      pastaBackup = DriveApp.createFolder('Dashboard_Backups');
    }
    
    DriveApp.getFileById(backup.getId()).moveTo(pastaBackup);
    
    console.log(`✅ Backup criado: ${nomeBackup}`);
    return nomeBackup;
    
  } catch (error) {
    console.error('❌ Erro no backup:', error);
    throw error;
  }
}
