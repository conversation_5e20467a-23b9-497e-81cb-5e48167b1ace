/**
 * DASHBOARD DE PROJETOS - GOOGLE APPS SCRIPT
 * Código principal para importar dados do data.txt e criar dashboard
 * Autor: Sistema de Gestão de Projetos FACEPE
 * Data: 2025
 */

// ==================== CONFIGURAÇÕES GLOBAIS ====================
const CONFIG = {
  // Nomes das abas
  ABA_DADOS: 'Dados_Projetos',
  ABA_DASHBOARD: 'Dashboard',
  ABA_METRICAS: 'Métricas',
  ABA_GRAFICOS: 'Gráficos',
  
  // Cores para status
  CORES: {
    'Concluído': '#4CAF50',
    'Em Andamento': '#FF9800', 
    'Atrasado': '#F44336',
    'Cancelado': '#9E9E9E',
    'Pausado': '#9C27B0'
  },
  
  // Configurações de formatação
  FONTE_TITULO: 'Roboto',
  TAMANHO_TITULO: 14,
  TAMANHO_TEXTO: 11
};

// ==================== FUNÇÃO PRINCIPAL ====================
/**
 * Função principal que inicializa todo o dashboard
 * Execute esta função para configurar tudo
 */
function inicializarDashboard() {
  try {
    console.log('🚀 Iniciando configuração do dashboard...');
    
    // 1. Importar dados do arquivo
    importarDadosDoArquivo();
    
    // 2. Criar estrutura das abas
    criarEstruturaPlanilha();
    
    // 3. Processar e calcular métricas
    calcularMetricas();
    
    // 4. Criar dashboard visual
    criarDashboardVisual();
    
    // 5. Configurar menu personalizado
    criarMenuCustomizado();
    
    // 6. Configurar triggers automáticos
    configurarTriggers();
    
    SpreadsheetApp.getUi().alert('✅ Dashboard configurado com sucesso!\n\nVerifique as abas criadas e o menu "📊 Dashboard Projetos"');
    
  } catch (error) {
    console.error('❌ Erro na inicialização:', error);
    SpreadsheetApp.getUi().alert('❌ Erro: ' + error.message);
  }
}

// ==================== IMPORTAÇÃO DE DADOS ====================
/**
 * Importa dados do arquivo data.txt
 * Você deve fazer upload do arquivo para o Google Drive primeiro
 */
function importarDadosDoArquivo() {
  try {
    console.log('📁 Importando dados do arquivo...');
    
    // Procurar arquivo data.txt no Drive
    const arquivos = DriveApp.getFilesByName('data.txt');
    
    if (!arquivos.hasNext()) {
      throw new Error('Arquivo data.txt não encontrado no Google Drive.\n\nPor favor:\n1. Faça upload do arquivo data.txt para o Google Drive\n2. Execute novamente esta função');
    }
    
    const arquivo = arquivos.next();
    const conteudo = arquivo.getBlob().getDataAsString();
    
    // Processar dados do arquivo
    const linhas = conteudo.split('\n');
    const cabecalho = linhas[0].split('|');
    
    // Preparar dados para a planilha
    const dados = [cabecalho];
    
    for (let i = 1; i < linhas.length; i++) {
      if (linhas[i].trim()) {
        dados.push(linhas[i].split('|'));
      }
    }
    
    // Inserir dados na planilha
    const planilha = SpreadsheetApp.getActiveSpreadsheet();
    let aba = planilha.getSheetByName(CONFIG.ABA_DADOS);
    
    if (!aba) {
      aba = planilha.insertSheet(CONFIG.ABA_DADOS);
    }
    
    // Limpar dados existentes
    aba.clear();
    
    // Inserir novos dados
    if (dados.length > 0) {
      aba.getRange(1, 1, dados.length, dados[0].length).setValues(dados);
      
      // Formatar cabeçalho
      formatarCabecalho(aba, dados[0].length);
      
      // Aplicar formatação condicional
      aplicarFormatacaoCondicional(aba, dados.length);
    }
    
    console.log(`✅ Importados ${dados.length - 1} projetos com sucesso`);
    
  } catch (error) {
    console.error('❌ Erro na importação:', error);
    throw error;
  }
}

// ==================== FORMATAÇÃO ====================
/**
 * Formata o cabeçalho da tabela de dados
 */
function formatarCabecalho(aba, numColunas) {
  const cabecalho = aba.getRange(1, 1, 1, numColunas);
  
  cabecalho
    .setBackground('#1f4e79')
    .setFontColor('#ffffff')
    .setFontWeight('bold')
    .setFontSize(CONFIG.TAMANHO_TITULO)
    .setHorizontalAlignment('center')
    .setVerticalAlignment('middle');
    
  // Ajustar largura das colunas
  aba.autoResizeColumns(1, numColunas);
  
  // Congelar primeira linha
  aba.setFrozenRows(1);
}

/**
 * Aplica formatação condicional baseada no status
 */
function aplicarFormatacaoCondicional(aba, numLinhas) {
  if (numLinhas <= 1) return;
  
  // Encontrar coluna de status (assumindo que é a 4ª coluna)
  const colunaStatus = 4;
  const rangeStatus = aba.getRange(2, colunaStatus, numLinhas - 1, 1);
  
  // Limpar regras existentes
  aba.clearConditionalFormatRules();
  
  const regras = [];
  
  // Criar regras para cada status
  Object.keys(CONFIG.CORES).forEach(status => {
    const regra = SpreadsheetApp.newConditionalFormatRule()
      .whenTextEqualTo(status)
      .setBackground(CONFIG.CORES[status])
      .setFontColor('#ffffff')
      .setRanges([rangeStatus])
      .build();
    regras.push(regra);
  });
  
  aba.setConditionalFormatRules(regras);
}

// ==================== MENU PERSONALIZADO ====================
/**
 * Cria menu personalizado na planilha
 */
function criarMenuCustomizado() {
  const ui = SpreadsheetApp.getUi();
  
  ui.createMenu('📊 Dashboard Projetos')
    .addItem('🔄 Atualizar Dados', 'atualizarDashboard')
    .addItem('📈 Recalcular Métricas', 'calcularMetricas')
    .addItem('📊 Recriar Gráficos', 'criarGraficos')
    .addSeparator()
    .addItem('📁 Reimportar Arquivo', 'importarDadosDoArquivo')
    .addItem('📧 Enviar Relatório', 'enviarRelatorio')
    .addSeparator()
    .addItem('⚙️ Configurações', 'abrirConfiguracoes')
    .addItem('❓ Ajuda', 'mostrarAjuda')
    .addToUi();
}

// ==================== TRIGGERS AUTOMÁTICOS ====================
/**
 * Configura triggers para atualização automática
 */
function configurarTriggers() {
  // Remover triggers existentes
  const triggers = ScriptApp.getProjectTriggers();
  triggers.forEach(trigger => {
    if (trigger.getHandlerFunction() === 'atualizarDashboardAutomatico') {
      ScriptApp.deleteTrigger(trigger);
    }
  });
  
  // Criar trigger para atualização diária
  ScriptApp.newTrigger('atualizarDashboardAutomatico')
    .timeBased()
    .everyDays(1)
    .atHour(8)
    .create();
}

// ==================== FUNÇÕES DE EVENTO ====================
/**
 * Executada quando a planilha é aberta
 */
function onOpen() {
  criarMenuCustomizado();
  
  // Verificar se o dashboard está configurado
  const planilha = SpreadsheetApp.getActiveSpreadsheet();
  const aba = planilha.getSheetByName(CONFIG.ABA_DASHBOARD);
  
  if (!aba) {
    SpreadsheetApp.getUi().alert(
      '👋 Bem-vindo ao Dashboard de Projetos!\n\n' +
      'Para começar, execute:\n' +
      'Menu "📊 Dashboard Projetos" → "🔄 Atualizar Dados"\n\n' +
      'Certifique-se de que o arquivo data.txt está no Google Drive.'
    );
  }
}

/**
 * Executada quando há edição na planilha
 */
function onEdit(e) {
  const aba = e.source.getActiveSheet();
  
  // Se editou a aba de dados, recalcular métricas
  if (aba.getName() === CONFIG.ABA_DADOS) {
    Utilities.sleep(1000); // Aguardar 1 segundo
    calcularMetricas();
  }
}

/**
 * Atualização automática diária
 */
function atualizarDashboardAutomatico() {
  try {
    atualizarDashboard();
  } catch (error) {
    console.error('Erro na atualização automática:', error);
  }
}

// ==================== FUNÇÕES AUXILIARES ====================
/**
 * Atualiza todo o dashboard
 */
function atualizarDashboard() {
  try {
    importarDadosDoArquivo();
    calcularMetricas();
    criarDashboardVisual();
    
    SpreadsheetApp.getUi().alert('✅ Dashboard atualizado com sucesso!');
  } catch (error) {
    SpreadsheetApp.getUi().alert('❌ Erro na atualização: ' + error.message);
  }
}

/**
 * Mostra ajuda para o usuário
 */
function mostrarAjuda() {
  const ajuda = `
📊 DASHBOARD DE PROJETOS - AJUDA

🔧 CONFIGURAÇÃO INICIAL:
1. Faça upload do arquivo data.txt para o Google Drive
2. Execute: Menu → "🔄 Atualizar Dados"

📁 FORMATO DO ARQUIVO data.txt:
- Separador: | (pipe)
- Primeira linha: cabeçalhos
- Demais linhas: dados dos projetos

🎯 FUNCIONALIDADES:
• Importação automática de dados
• Cálculo de métricas em tempo real
• Gráficos interativos
• Relatórios automáticos
• Atualização diária

❓ PROBLEMAS COMUNS:
• Arquivo não encontrado: Verifique se data.txt está no Drive
• Dados incorretos: Verifique formato do arquivo
• Gráficos não aparecem: Execute "📊 Recriar Gráficos"

📧 SUPORTE: Verifique os logs no Apps Script Editor
  `;
  
  SpreadsheetApp.getUi().alert(ajuda);
}
