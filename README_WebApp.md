# 🌐 Dashboard de Projetos FACEPE - Web App

Sistema completo de dashboard web desenvolvido em Google Apps Script que permite upload de arquivos Excel e visualização de dados em tempo real.

## 🎯 Características Principais

- **📁 Upload de Excel**: Aceita arquivos .xlsx e .xls via drag & drop
- **🌐 Interface Web**: Dashboard responsivo e moderno
- **📊 Gráficos Interativos**: Visualizações com Chart.js
- **📱 Mobile-First**: Totalmente responsivo
- **🔄 Processamento Automático**: Converte Excel para dados estruturados
- **🎨 Design Moderno**: Interface glassmorphism com gradientes

## 📁 Estrutura dos Arquivos

```
📂 Web App Dashboard
├── 📄 WebAppCompleta.gs      # Código backend Google Apps Script
├── 📄 WebAppIndex.html       # Interface HTML principal
├── 📄 data.txt               # Arquivo de exemplo (opcional)
└── 📄 README_WebApp.md       # Este arquivo de instruções
```

## 🚀 Instalação e Deploy

### Passo 1: Criar Projeto Google Apps Script

1. **Acesse [script.google.com](https://script.google.com)**
2. **Clique em "Novo projeto"**
3. **Renomeie para "Dashboard Projetos FACEPE"**

### Passo 2: Configurar Arquivos

1. **Substituir Code.gs**:
   - Cole o conteúdo de `WebAppCompleta.gs`
   - Salve o arquivo

2. **Criar arquivo HTML**:
   - Clique em `+` → `Arquivo HTML`
   - Nomeie como `index`
   - Cole o conteúdo de `WebAppIndex.html`
   - Salve o arquivo

### Passo 3: Configurar Permissões

1. **Ativar APIs necessárias**:
   - No Apps Script, vá em `Serviços`
   - Adicione `Google Drive API`
   - Adicione `Google Sheets API`

2. **Configurar OAuth**:
   - Execute qualquer função para autorizar
   - Aceite todas as permissões solicitadas

### Passo 4: Deploy da Web App

1. **Fazer Deploy**:
   - Clique em `Implantar` → `Nova implantação`
   - Tipo: `Aplicativo da Web`
   - Executar como: `Eu`
   - Quem tem acesso: `Qualquer pessoa` (ou conforme necessário)

2. **Obter URL**:
   - Copie a URL da web app
   - Teste o acesso

## 🎛️ Como Usar

### Interface Principal

#### 1. **Tela de Upload**
- 📁 **Área de Upload**: Drag & drop ou clique para selecionar
- 📊 **Formatos Aceitos**: .xlsx, .xls
- 🔄 **Processamento**: Barra de progresso em tempo real
- ✅ **Feedback**: Alertas de sucesso/erro

#### 2. **Dashboard Interativo**
- 📈 **Cards de Métricas**: Total, orçamento, progresso
- 🎯 **Status Visual**: Distribuição colorida por status
- 📊 **Gráficos**: Pizza (status) e barras (categorias)
- 📱 **Responsivo**: Adapta-se a qualquer tela

### Fluxo de Uso

1. **Acesse a URL da web app**
2. **Faça upload do arquivo Excel**
3. **Aguarde o processamento**
4. **Visualize o dashboard gerado**
5. **Interaja com os gráficos**

## 📊 Formato dos Dados Excel

### Estrutura Esperada

| Coluna | Tipo | Descrição | Exemplo |
|--------|------|-----------|---------|
| ID | Texto | Identificador único | APQ-1386-21 |
| Título | Texto | Nome do projeto | DESENVOLVIMENTO TECNOLÓGICO |
| Responsável | Texto | Nome do responsável | João Silva |
| Status | Texto | Status atual | Em Andamento |
| Data_Início | Data | Data de início | 15/01/2024 |
| Data_Fim | Data | Data prevista | 01/12/2025 |
| Orçamento | Número | Valor em reais | 150000 |
| Progresso | Número | Percentual (0-100) | 75 |
| Categoria | Texto | Categoria do projeto | Tecnologia |
| Avaliador | Texto | Nome do avaliador | Maria Santos |

### Formatos de Data Aceitos
- ✅ **DD/MM/YYYY**: 15/01/2024
- ✅ **YYYY-MM-DD**: 2024-01-15
- ✅ **Formato Excel**: Datas nativas do Excel

### Status Válidos
- ✅ **Concluído**: Projeto finalizado
- 🔄 **Em Andamento**: Projeto em execução
- ⚠️ **Atrasado**: Projeto com atraso
- ❌ **Cancelado**: Projeto cancelado
- ⏸️ **Pausado**: Projeto temporariamente pausado

## 🎨 Funcionalidades da Interface

### Design Responsivo
- **Desktop**: Layout em grid com múltiplas colunas
- **Tablet**: Adaptação automática do grid
- **Mobile**: Layout em coluna única

### Interatividade
- **Drag & Drop**: Upload intuitivo de arquivos
- **Hover Effects**: Animações suaves nos cards
- **Loading States**: Indicadores visuais de carregamento
- **Alerts**: Feedback visual para ações do usuário

### Gráficos Interativos
- **Chart.js**: Biblioteca moderna de gráficos
- **Responsivos**: Adaptam-se ao tamanho da tela
- **Cores Personalizadas**: Baseadas no status dos projetos
- **Tooltips**: Informações detalhadas ao passar o mouse

## 🔧 Personalização

### Cores dos Status
```javascript
CORES: {
  'Concluído': '#4CAF50',      // Verde
  'Em Andamento': '#FF9800',   // Laranja
  'Atrasado': '#F44336',       // Vermelho
  'Cancelado': '#9E9E9E',      // Cinza
  'Pausado': '#9C27B0'         // Roxo
}
```

### Modificar Layout
- **CSS**: Edite as classes no arquivo HTML
- **Grid**: Ajuste `grid-template-columns` para diferentes layouts
- **Cores**: Modifique as variáveis CSS para nova paleta

### Adicionar Métricas
1. **Backend**: Adicione cálculos em `obterMetricasCompletas()`
2. **Frontend**: Crie novos cards no HTML
3. **JavaScript**: Implemente a exibição dos dados

## 🚨 Troubleshooting

### Problemas Comuns

#### 1. **Erro "doGet not found"**
```
❌ Script function not found: doGet
```
**Solução**: Certifique-se de que o arquivo `WebAppCompleta.gs` está correto e salvo.

#### 2. **Upload não funciona**
```
❌ Erro ao processar arquivo
```
**Soluções**:
- Verifique se as APIs estão ativadas
- Confirme as permissões do Apps Script
- Teste com arquivo Excel menor

#### 3. **Gráficos não aparecem**
```
❌ Charts não carregam
```
**Soluções**:
- Verifique conexão com internet (Chart.js CDN)
- Confirme se há dados válidos
- Teste em navegador diferente

#### 4. **Erro de permissões**
```
❌ Access denied
```
**Soluções**:
- Reautorize o Apps Script
- Verifique configurações de compartilhamento
- Confirme permissões do Drive API

### Logs e Debug

1. **Apps Script Console**:
   - `Execuções` → Ver logs detalhados
   - `console.log()` para debug

2. **Browser DevTools**:
   - F12 → Console para erros JavaScript
   - Network tab para problemas de carregamento

## 📈 Métricas Calculadas

### KPIs Automáticos
- **Total de Projetos**: Contagem total
- **Orçamento Total**: Soma de todos os valores
- **Progresso Médio**: Média ponderada
- **Distribuição por Status**: Percentuais automáticos

### Análises Visuais
- **Gráfico Pizza**: Distribuição de status
- **Gráfico Barras**: Projetos por categoria
- **Cards Coloridos**: Status com cores específicas

## 🔒 Segurança

### Configurações Recomendadas
- **Acesso Restrito**: Configure "Qualquer pessoa na organização"
- **HTTPS**: Apps Script usa HTTPS por padrão
- **Validação**: Arquivos são validados antes do processamento

### Dados Temporários
- **Arquivos Excel**: Removidos após processamento
- **Conversões**: Planilhas temporárias são excluídas
- **Cache**: Dados ficam apenas na sessão

## 🚀 Próximos Passos

### Melhorias Futuras
- [ ] **Múltiplos Arquivos**: Upload de vários arquivos
- [ ] **Histórico**: Versioning de uploads
- [ ] **Filtros**: Filtros interativos no dashboard
- [ ] **Exportação**: Download de relatórios
- [ ] **Notificações**: Alertas por email
- [ ] **API**: Endpoints para integração

### Integração
- **Google Sheets**: Sincronização bidirecional
- **Google Drive**: Backup automático
- **Gmail**: Relatórios por email
- **Calendar**: Marcos de projeto

---

## 🎉 **WEB APP PRONTA PARA USO!**

Sua aplicação web está configurada e pronta para receber uploads de Excel e gerar dashboards interativos em tempo real!

**🔗 Próximo passo**: Faça o deploy e compartilhe a URL com sua equipe!
