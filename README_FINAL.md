# 🎉 Dashboard de Projetos FACEPE - VERSÃO FINAL

Sistema completo de dashboard web que conecta diretamente com planilhas do Google Sheets via link.

## 📁 Arquivos Finais

```
📂 Dashboard Completo
├── 📄 DashboardCompleto_Final.gs    # CÓDIGO BACKEND COMPLETO
├── 📄 IndexCompleto_Final.html      # CÓDIGO FRONTEND COMPLETO
└── 📄 README_FINAL.md               # Este arquivo
```

## 🚀 Instalação Rápida

### **Passo 1: Criar Projeto Apps Script**
1. Acesse [script.google.com](https://script.google.com)
2. Clique em **"Novo projeto"**
3. Renomeie para **"Dashboard Projetos FACEPE"**

### **Passo 2: Adicionar Código Backend**
1. **Substitua o conteúdo de `Code.gs`**:
   - Apague todo o conteúdo existente
   - Cole o conteúdo completo de `DashboardCompleto_Final.gs`
   - Salve (Ctrl+S)

### **Passo 3: Adicionar <PERSON>end**
1. **Criar arquivo HTML**:
   - Clique em `+` → `Arquivo HTML`
   - Nomeie como `index`
   - Cole o conteúdo completo de `IndexCompleto_Final.html`
   - Salve (Ctrl+S)

### **Passo 4: Deploy da Web App**
1. **Fazer Deploy**:
   - Clique em `Implantar` → `Nova implantação`
   - Tipo: `Aplicativo da Web`
   - Executar como: `Eu`
   - Quem tem acesso: `Qualquer pessoa` (ou conforme necessário)
   - Clique em `Implantar`

2. **Copiar URL**:
   - Copie a URL gerada
   - Esta é a URL da sua web app

## 📊 Como Usar

### **1. Preparar Planilha Google Sheets**

#### **Estrutura da Planilha:**
Crie uma planilha com as seguintes colunas na **primeira linha**:

| A | B | C | D | E | F | G | H | I | J |
|---|---|---|---|---|---|---|---|---|---|
| ID | Título | Responsável | Status | Data_Início | Data_Fim | Orçamento | Progresso | Categoria | Avaliador |

#### **Exemplo de Dados:**
```
APQ-1386-21 | DESENVOLVIMENTO TECNOLÓGICO | João Silva | Em Andamento | 15/01/2024 | 01/12/2025 | 150000 | 75 | Tecnologia | Maria Santos
APQ-1387-21 | PESQUISA BIOMÉDICA | Ana Costa | Concluído | 01/03/2024 | 30/11/2024 | 200000 | 100 | Saúde | Pedro Lima
```

#### **Compartilhar Planilha:**
1. Clique em **"Compartilhar"**
2. **"Acesso geral"** → **"Qualquer pessoa com o link"**
3. Permissão: **"Visualizador"** ou **"Editor"**
4. **"Copiar link"**

### **2. Usar a Web App**

1. **Acesse a URL** da web app
2. **Cole o link** da planilha no campo
3. **Clique em "Testar Conexão"**
4. **Verifique as informações** da planilha
5. **Clique em "Conectar Planilha"**
6. **Visualize o dashboard** gerado automaticamente

## 🎯 Funcionalidades

### **📊 Dashboard Interativo:**
- **Cards de Métricas**: Total de projetos, orçamento total, progresso médio
- **Status Coloridos**: Distribuição visual por status dos projetos
- **Gráfico de Pizza**: Distribuição por status com cores personalizadas
- **Gráfico de Barras**: Número de projetos por categoria

### **🔗 Conexão Inteligente:**
- **Validação de Link**: Verifica formato em tempo real
- **Teste de Conexão**: Valida acesso antes de importar
- **Informações da Planilha**: Mostra detalhes antes da conexão
- **Processamento Automático**: Importa e calcula métricas automaticamente

### **🎨 Interface Moderna:**
- **Design Responsivo**: Funciona em desktop, tablet e mobile
- **Glassmorphism**: Efeitos de vidro e blur modernos
- **Animações Suaves**: Transições e hover effects
- **Feedback Visual**: Cores e ícones indicam status

## 🔧 Status Suportados

O sistema reconhece automaticamente os seguintes status com cores específicas:

- 🟢 **Concluído** - Verde (#4CAF50)
- 🟠 **Em Andamento** - Laranja (#FF9800)
- 🔴 **Atrasado** - Vermelho (#F44336)
- ⚫ **Cancelado** - Cinza (#9E9E9E)
- 🟣 **Pausado** - Roxo (#9C27B0)

## 📋 Formatos de Data Aceitos

- ✅ **DD/MM/YYYY**: 15/01/2024
- ✅ **YYYY-MM-DD**: 2024-01-15
- ✅ **Formato Excel**: Datas nativas do Excel

## 🔗 Formatos de Link Aceitos

```
✅ https://docs.google.com/spreadsheets/d/1ABC123.../edit
✅ https://docs.google.com/spreadsheets/d/1ABC123...
✅ 1ABC123DEF456... (apenas o ID)
```

## 🚨 Troubleshooting

### **Problemas Comuns:**

#### **1. "Não foi possível acessar a planilha"**
- ✅ Verifique se a planilha está compartilhada
- ✅ Confirme permissões de "Qualquer pessoa com o link"
- ✅ Teste o link em uma aba anônima

#### **2. "Link inválido"**
- ✅ Use o link completo do Google Sheets
- ✅ Copie diretamente do botão "Compartilhar"
- ✅ Verifique se não há caracteres extras

#### **3. "Planilha está vazia"**
- ✅ Adicione cabeçalhos na primeira linha
- ✅ Insira dados nas linhas seguintes
- ✅ Verifique se está na aba correta

#### **4. Gráficos não aparecem**
- ✅ Verifique conexão com internet (Chart.js CDN)
- ✅ Confirme se há dados válidos
- ✅ Recarregue a página

## 🎯 Vantagens

- ✅ **Sem Upload**: Conecta diretamente com Google Sheets
- ✅ **Tempo Real**: Dados sempre atualizados
- ✅ **Seguro**: Apenas leitura, sem modificações
- ✅ **Flexível**: Aceita qualquer estrutura de planilha
- ✅ **Rápido**: Processamento instantâneo
- ✅ **Fácil**: Interface intuitiva

## 🔄 Fluxo de Uso

```
1. Criar planilha no Google Sheets
2. Estruturar dados com cabeçalhos
3. Compartilhar com link público
4. Acessar web app
5. Colar link da planilha
6. Testar conexão
7. Conectar e visualizar dashboard
```

## 📈 Métricas Calculadas Automaticamente

- **Total de Projetos**: Contagem total
- **Orçamento Total**: Soma de todos os valores
- **Progresso Médio**: Média ponderada de todos os projetos
- **Distribuição por Status**: Percentuais automáticos
- **Projetos por Categoria**: Agrupamento automático

## 🎨 Personalização

### **Cores dos Status:**
Para alterar as cores, edite no arquivo `.gs`:
```javascript
CORES: {
  'Concluído': '#4CAF50',      // Verde
  'Em Andamento': '#FF9800',   // Laranja
  'Atrasado': '#F44336',       // Vermelho
  'Cancelado': '#9E9E9E',      // Cinza
  'Pausado': '#9C27B0'         // Roxo
}
```

### **Layout:**
Para modificar o layout, edite o CSS no arquivo `.html`.

## 🔒 Segurança

- ✅ **Apenas Leitura**: Não modifica dados da planilha original
- ✅ **HTTPS**: Conexão segura por padrão
- ✅ **Temporário**: Dados ficam apenas na sessão
- ✅ **Permissões**: Controle de acesso via Google Apps Script

## 🚀 Deploy e Compartilhamento

### **URL da Web App:**
Após o deploy, você receberá uma URL como:
```
https://script.google.com/macros/s/ABC123.../exec
```

### **Compartilhar:**
- Envie esta URL para sua equipe
- Cada pessoa pode acessar e conectar suas próprias planilhas
- Não há limite de usuários simultâneos

## 🎉 **PRONTO PARA USAR!**

Agora você tem um sistema completo de dashboard que:
- ✅ Conecta diretamente com Google Sheets
- ✅ Gera gráficos interativos automaticamente
- ✅ Calcula métricas em tempo real
- ✅ Funciona em qualquer dispositivo
- ✅ É totalmente responsivo e moderno

**🔗 Próximo passo**: Faça o deploy e comece a usar com suas planilhas do Google Sheets!

---

**Desenvolvido para FACEPE - 2025**
*Sistema de Acompanhamento e Análise de Projetos*
