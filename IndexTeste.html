<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Teste de Comunicação - Dashboard FACEPE</title>
    
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #1976d2;
            color: white;
            border-radius: 10px;
        }
        
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #1976d2;
        }
        
        .btn {
            background: #1976d2;
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            display: inline-block;
        }
        
        .btn:hover {
            background: #1565c0;
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .result {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border: 1px solid #1976d2;
            min-height: 50px;
        }
        
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        
        .log {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            border: 1px solid #e9ecef;
            max-height: 200px;
            overflow-y: auto;
        }
        
        input[type="url"] {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            margin: 10px 0;
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.loading {
            background: #fff3cd;
            color: #856404;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Teste de Comunicação</h1>
            <p>Diagnóstico da Web App Dashboard FACEPE</p>
        </div>
        
        <div class="test-section">
            <h3>📋 Informações do Sistema</h3>
            <div id="systemInfo" class="result">Carregando...</div>
        </div>
        
        <div class="test-section">
            <h3>🔗 Teste 1: Comunicação Básica</h3>
            <p>Testa se o google.script.run está funcionando</p>
            <button class="btn" onclick="testeBasico()">🚀 Testar Comunicação</button>
            <div id="resultado1" class="result">Clique no botão para testar</div>
        </div>
        
        <div class="test-section">
            <h3>📊 Teste 2: Verificar Dados</h3>
            <p>Verifica se existem dados carregados</p>
            <button class="btn" onclick="verificarDados()">📋 Verificar Dados</button>
            <div id="resultado2" class="result">Clique no botão para verificar</div>
        </div>
        
        <div class="test-section">
            <h3>🔗 Teste 3: Testar Link do Google Sheets</h3>
            <p>Testa conexão com uma planilha específica</p>
            <input type="url" id="linkTeste" placeholder="Cole o link da sua planilha aqui..." />
            <button class="btn" onclick="testarLink()">🔍 Testar Link</button>
            <div id="resultado3" class="result">Insira um link e clique no botão</div>
        </div>
        
        <div class="test-section">
            <h3>📝 Log de Eventos</h3>
            <div id="logContainer" class="log">Aguardando eventos...</div>
            <button class="btn" onclick="limparLog()">🗑️ Limpar Log</button>
        </div>
        
        <div class="test-section">
            <h3>🔄 Status da Aplicação</h3>
            <div id="statusApp" class="status">Inicializando...</div>
        </div>
    </div>

    <script>
        // Log personalizado
        function log(mensagem) {
            const agora = new Date().toLocaleTimeString();
            const logContainer = document.getElementById('logContainer');
            logContainer.innerHTML += `[${agora}] ${mensagem}<br>`;
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(mensagem);
        }
        
        // Atualizar status
        function setStatus(mensagem, tipo = 'loading') {
            const statusDiv = document.getElementById('statusApp');
            statusDiv.textContent = mensagem;
            statusDiv.className = `status ${tipo}`;
            log(`Status: ${mensagem}`);
        }
        
        // Inicialização
        document.addEventListener('DOMContentLoaded', function() {
            log('DOM carregado');
            setStatus('Aplicação carregada', 'success');
            
            // Informações do sistema
            const systemInfo = document.getElementById('systemInfo');
            systemInfo.innerHTML = `
                <strong>URL:</strong> ${window.location.href}<br>
                <strong>User Agent:</strong> ${navigator.userAgent}<br>
                <strong>Timestamp:</strong> ${new Date().toLocaleString('pt-BR')}<br>
                <strong>Google Script:</strong> ${typeof google !== 'undefined' && google.script ? 'Disponível' : 'Não disponível'}
            `;
            
            // Verificar se google.script está disponível
            if (typeof google === 'undefined' || !google.script) {
                log('❌ ERRO: google.script não está disponível!');
                setStatus('Erro: google.script não disponível', 'error');
            } else {
                log('✅ google.script está disponível');
                setStatus('google.script disponível', 'success');
            }
        });
        
        // Teste 1: Comunicação básica
        function testeBasico() {
            log('Iniciando teste básico...');
            setStatus('Testando comunicação básica...', 'loading');
            
            const resultado = document.getElementById('resultado1');
            resultado.textContent = 'Testando...';
            resultado.className = 'result';
            
            // Verificar se google.script existe
            if (typeof google === 'undefined' || !google.script || !google.script.run) {
                const erro = 'google.script.run não está disponível';
                log(`❌ ${erro}`);
                resultado.textContent = `ERRO: ${erro}`;
                resultado.className = 'result error';
                setStatus('Erro na comunicação', 'error');
                return;
            }
            
            try {
                // Tentar chamar uma função simples
                google.script.run
                    .withSuccessHandler(function(resposta) {
                        log(`✅ Sucesso: ${resposta}`);
                        resultado.textContent = `✅ Comunicação OK: ${resposta}`;
                        resultado.className = 'result success';
                        setStatus('Comunicação funcionando', 'success');
                    })
                    .withFailureHandler(function(erro) {
                        log(`❌ Erro: ${erro.message}`);
                        resultado.textContent = `❌ Erro: ${erro.message}`;
                        resultado.className = 'result error';
                        setStatus('Erro na comunicação', 'error');
                    })
                    .testeConexao(); // Função que vamos criar no .gs
                    
            } catch (error) {
                log(`❌ Exceção: ${error.message}`);
                resultado.textContent = `❌ Exceção: ${error.message}`;
                resultado.className = 'result error';
                setStatus('Exceção na comunicação', 'error');
            }
        }
        
        // Teste 2: Verificar dados
        function verificarDados() {
            log('Verificando dados...');
            setStatus('Verificando dados...', 'loading');
            
            const resultado = document.getElementById('resultado2');
            resultado.textContent = 'Verificando...';
            resultado.className = 'result';
            
            if (typeof google === 'undefined' || !google.script || !google.script.run) {
                resultado.textContent = 'ERRO: google.script não disponível';
                resultado.className = 'result error';
                return;
            }
            
            try {
                google.script.run
                    .withSuccessHandler(function(dados) {
                        log(`✅ Dados recebidos: ${JSON.stringify(dados)}`);
                        resultado.innerHTML = `
                            <strong>✅ Dados encontrados:</strong><br>
                            Tem dados: ${dados.temDados}<br>
                            Total projetos: ${dados.totalProjetos}<br>
                            Detalhes: ${JSON.stringify(dados, null, 2)}
                        `;
                        resultado.className = 'result success';
                        setStatus('Dados verificados', 'success');
                    })
                    .withFailureHandler(function(erro) {
                        log(`❌ Erro ao verificar dados: ${erro.message}`);
                        resultado.textContent = `❌ Erro: ${erro.message}`;
                        resultado.className = 'result error';
                        setStatus('Erro ao verificar dados', 'error');
                    })
                    .verificarStatusDados();
                    
            } catch (error) {
                log(`❌ Exceção: ${error.message}`);
                resultado.textContent = `❌ Exceção: ${error.message}`;
                resultado.className = 'result error';
            }
        }
        
        // Teste 3: Testar link
        function testarLink() {
            const link = document.getElementById('linkTeste').value.trim();
            
            if (!link) {
                alert('Por favor, insira um link');
                return;
            }
            
            log(`Testando link: ${link}`);
            setStatus('Testando link...', 'loading');
            
            const resultado = document.getElementById('resultado3');
            resultado.textContent = 'Testando link...';
            resultado.className = 'result';
            
            if (typeof google === 'undefined' || !google.script || !google.script.run) {
                resultado.textContent = 'ERRO: google.script não disponível';
                resultado.className = 'result error';
                return;
            }
            
            try {
                google.script.run
                    .withSuccessHandler(function(resposta) {
                        log(`✅ Teste de link: ${JSON.stringify(resposta)}`);
                        if (resposta.sucesso) {
                            resultado.innerHTML = `
                                <strong>✅ Link válido:</strong><br>
                                Nome: ${resposta.nomePlanilha}<br>
                                Aba: ${resposta.nomeAba}<br>
                                Linhas: ${resposta.totalLinhas}<br>
                                Colunas: ${resposta.colunas.length}
                            `;
                            resultado.className = 'result success';
                            setStatus('Link testado com sucesso', 'success');
                        } else {
                            resultado.textContent = `❌ ${resposta.mensagem}`;
                            resultado.className = 'result error';
                            setStatus('Erro no link', 'error');
                        }
                    })
                    .withFailureHandler(function(erro) {
                        log(`❌ Erro no teste de link: ${erro.message}`);
                        resultado.textContent = `❌ Erro: ${erro.message}`;
                        resultado.className = 'result error';
                        setStatus('Erro no teste de link', 'error');
                    })
                    .testarConexaoPlanilha(link);
                    
            } catch (error) {
                log(`❌ Exceção: ${error.message}`);
                resultado.textContent = `❌ Exceção: ${error.message}`;
                resultado.className = 'result error';
            }
        }
        
        // Limpar log
        function limparLog() {
            document.getElementById('logContainer').innerHTML = 'Log limpo...';
            log('Log limpo pelo usuário');
        }
        
        // Capturar erros globais
        window.addEventListener('error', function(e) {
            log(`❌ ERRO GLOBAL: ${e.message} em ${e.filename}:${e.lineno}`);
            setStatus('Erro JavaScript detectado', 'error');
        });
        
        // Log inicial
        log('Script carregado e pronto');
    </script>
</body>
</html>
