<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Dashboard de Projetos FACEPE - Mínimo</title>
    
    <!-- Chart.js Library -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .header h1 {
            color: #1f4e79;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .link-area {
            border: 3px dashed #1976d2;
            border-radius: 15px;
            padding: 40px 20px;
            background: #f8f9ff;
            text-align: center;
        }
        
        .link-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #1976d2;
            border-radius: 10px;
            font-size: 16px;
            margin-bottom: 15px;
        }
        
        .btn {
            background: linear-gradient(45deg, #1976d2, #42a5f5);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            margin: 10px;
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .btn-success {
            background: linear-gradient(45deg, #4caf50, #81c784);
        }
        
        .mapping-simple {
            display: none;
            text-align: left;
        }
        
        .mapping-row {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #1976d2;
        }
        
        .mapping-label {
            flex: 1;
            font-weight: bold;
            color: #1f4e79;
        }
        
        .mapping-select {
            flex: 2;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            background: white;
        }
        
        .required {
            border-left-color: #f44336;
        }
        
        .optional {
            border-left-color: #4caf50;
        }
        
        .dashboard {
            display: none;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .card-icon {
            font-size: 2rem;
            margin-right: 15px;
        }
        
        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1f4e79;
        }
        
        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 5px;
        }
        
        .metric-label {
            color: #666;
            font-size: 1rem;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            margin: 8px 0;
            border-radius: 8px;
            font-weight: 500;
        }
        
        .chart-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .chart-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1f4e79;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 500;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #1976d2;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .info-box {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #1976d2;
        }
        
        .info-box h4 {
            color: #1976d2;
            margin-bottom: 10px;
        }
        
        .columns-preview {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .column-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .column-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>📊 Dashboard de Projetos FACEPE</h1>
            <p>Versão Simplificada - Mínimo 2 Colunas</p>
        </div>
        
        <!-- Link Section -->
        <div id="linkSection" class="section">
            <div class="link-area">
                <h3 style="color: #1976d2; margin-bottom: 20px;">🔗 Conectar Planilha</h3>
                <input type="url" id="linkInput" class="link-input" 
                       placeholder="Cole o link da sua planilha Google Sheets..." />
                <button class="btn" onclick="conectarPlanilha()" id="btnConectar">
                    🔍 Conectar e Mapear
                </button>
            </div>
            <div id="alertContainer"></div>
        </div>
        
        <!-- Mapping Section -->
        <div id="mappingSection" class="section mapping-simple">
            <h3 style="color: #1f4e79; margin-bottom: 20px; text-align: center;">
                🎯 Mapeamento Rápido
            </h3>
            <p style="text-align: center; color: #666; margin-bottom: 30px;">
                Selecione apenas as 2 colunas principais para gerar o dashboard
            </p>
            
            <div id="columnsPreview" class="info-box">
                <h4>📋 Colunas Encontradas na Planilha:</h4>
                <div id="columnsList" class="columns-preview"></div>
            </div>
            
            <div class="mapping-row required">
                <div class="mapping-label">
                    🆔 ID/Código do Projeto (Obrigatório)
                </div>
                <select class="mapping-select" id="map_id">
                    <option value="">Selecione uma coluna...</option>
                </select>
            </div>
            
            <div class="mapping-row required">
                <div class="mapping-label">
                    📝 Nome/Título do Projeto (Obrigatório)
                </div>
                <select class="mapping-select" id="map_titulo">
                    <option value="">Selecione uma coluna...</option>
                </select>
            </div>
            
            <div class="mapping-row optional">
                <div class="mapping-label">
                    🎯 Status/Situação (Opcional)
                </div>
                <select class="mapping-select" id="map_status">
                    <option value="">Selecione uma coluna...</option>
                </select>
            </div>
            
            <div class="mapping-row optional">
                <div class="mapping-label">
                    📂 Categoria/Tipo (Opcional)
                </div>
                <select class="mapping-select" id="map_categoria">
                    <option value="">Selecione uma coluna...</option>
                </select>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <button class="btn btn-success" onclick="gerarDashboard()" id="btnGerar" disabled>
                    🚀 Gerar Dashboard
                </button>
            </div>
        </div>
        
        <!-- Dashboard Section -->
        <div id="dashboardSection" class="section dashboard">
            <h3 style="color: #1f4e79; margin-bottom: 30px; text-align: center;">
                📊 Dashboard Gerado
            </h3>
            
            <!-- Metrics Cards -->
            <div class="dashboard-grid">
                <div class="card">
                    <div class="card-header">
                        <div class="card-icon">📊</div>
                        <div class="card-title">Total de Projetos</div>
                    </div>
                    <div class="metric-value" id="totalProjetos">0</div>
                    <div class="metric-label">projetos encontrados</div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <div class="card-icon">🎯</div>
                        <div class="card-title">Status dos Projetos</div>
                    </div>
                    <div id="statusContainer"></div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <div class="card-icon">📂</div>
                        <div class="card-title">Categorias</div>
                    </div>
                    <div id="categoriaContainer"></div>
                </div>
            </div>
            
            <!-- Charts -->
            <div class="chart-container">
                <div class="chart-title">📊 Distribuição por Status</div>
                <canvas id="statusChart" width="400" height="200"></canvas>
            </div>
            
            <div class="chart-container">
                <div class="chart-title">📂 Projetos por Categoria</div>
                <canvas id="categoriaChart" width="400" height="200"></canvas>
            </div>
            
            <!-- Botão para nova conexão -->
            <div style="text-align: center; margin-top: 30px;">
                <button class="btn" onclick="novaConexao()">
                    🔄 Conectar Nova Planilha
                </button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // Variáveis globais
        let dadosDashboard = [];
        let metricas = {};
        let graficos = {};
        let colunasPlanilha = [];
        let dadosBrutos = [];

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Dashboard mínimo carregado');

            const linkInput = document.getElementById('linkInput');
            if (linkInput) {
                linkInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        conectarPlanilha();
                    }
                });
            }

            // Event listeners para mapeamento
            const mappingSelects = document.querySelectorAll('.mapping-select');
            mappingSelects.forEach(select => {
                select.addEventListener('change', verificarMapeamento);
            });
        });

        // Conectar planilha diretamente
        function conectarPlanilha() {
            const link = document.getElementById('linkInput').value.trim();

            if (!link) {
                mostrarAlerta('Por favor, insira o link da planilha', 'error');
                return;
            }

            const btnConectar = document.getElementById('btnConectar');
            btnConectar.disabled = true;
            btnConectar.innerHTML = '<span class="loading"></span> Conectando...';

            google.script.run
                .withSuccessHandler(function(resultado) {
                    btnConectar.disabled = false;
                    btnConectar.innerHTML = '🔍 Conectar e Mapear';

                    if (resultado.sucesso) {
                        mostrarAlerta('Planilha conectada! Agora mapeie as colunas.', 'success');

                        // Armazenar dados
                        colunasPlanilha = resultado.colunas;
                        dadosBrutos = resultado.dadosBrutos;

                        // Mostrar colunas encontradas
                        mostrarColunasEncontradas(resultado.colunas);

                        // Preencher selects
                        preencherSelects(resultado.colunas);

                        // Tentar mapeamento automático
                        tentarMapeamentoAutomatico(resultado.colunas);

                        // Mostrar seção de mapeamento
                        mostrarMapeamento();

                    } else {
                        mostrarAlerta(resultado.mensagem, 'error');
                    }
                })
                .withFailureHandler(function(erro) {
                    btnConectar.disabled = false;
                    btnConectar.innerHTML = '🔍 Conectar e Mapear';
                    mostrarAlerta('Erro ao conectar: ' + erro.message, 'error');
                })
                .obterDadosParaMapeamento(link);
        }

        // Mostrar colunas encontradas
        function mostrarColunasEncontradas(colunas) {
            const container = document.getElementById('columnsList');
            container.innerHTML = '';

            colunas.forEach((coluna, index) => {
                const item = document.createElement('div');
                item.className = 'column-item';
                item.innerHTML = `
                    <span><strong>Coluna ${String.fromCharCode(65 + index)}:</strong> ${coluna}</span>
                    <span style="color: #666;">${index + 1}</span>
                `;
                container.appendChild(item);
            });
        }

        // Preencher selects
        function preencherSelects(colunas) {
            const selects = ['map_id', 'map_titulo', 'map_status', 'map_categoria'];

            selects.forEach(selectId => {
                const select = document.getElementById(selectId);
                select.innerHTML = '<option value="">Selecione uma coluna...</option>';

                colunas.forEach((coluna, index) => {
                    const option = document.createElement('option');
                    option.value = index;
                    option.textContent = `${coluna} (Coluna ${String.fromCharCode(65 + index)})`;
                    select.appendChild(option);
                });
            });
        }

        // Tentar mapeamento automático simples
        function tentarMapeamentoAutomatico(colunas) {
            const mapeamentos = {
                'map_id': ['id', 'código', 'codigo', 'identificador', 'number', 'num'],
                'map_titulo': ['título', 'titulo', 'nome', 'name', 'projeto', 'project'],
                'map_status': ['status', 'situação', 'situacao', 'estado', 'state'],
                'map_categoria': ['categoria', 'category', 'tipo', 'type', 'área', 'area']
            };

            Object.keys(mapeamentos).forEach(selectId => {
                const select = document.getElementById(selectId);
                const palavrasChave = mapeamentos[selectId];

                const colunaEncontrada = colunas.findIndex(coluna => {
                    const colunaNormalizada = coluna.toLowerCase()
                        .normalize('NFD')
                        .replace(/[\u0300-\u036f]/g, '');

                    return palavrasChave.some(palavra =>
                        colunaNormalizada.includes(palavra.toLowerCase())
                    );
                });

                if (colunaEncontrada !== -1) {
                    select.value = colunaEncontrada;
                    select.style.borderColor = '#4caf50';
                }
            });

            verificarMapeamento();
        }

        // Verificar mapeamento (apenas ID e Título obrigatórios)
        function verificarMapeamento() {
            const idSelect = document.getElementById('map_id');
            const tituloSelect = document.getElementById('map_titulo');
            const btnGerar = document.getElementById('btnGerar');

            const mapeamentoCompleto = idSelect.value !== '' && tituloSelect.value !== '';

            btnGerar.disabled = !mapeamentoCompleto;

            if (mapeamentoCompleto) {
                btnGerar.style.background = 'linear-gradient(45deg, #4caf50, #81c784)';
                btnGerar.textContent = '🚀 Gerar Dashboard';
            } else {
                btnGerar.style.background = '#ccc';
                btnGerar.textContent = '🚀 Selecione ID e Título';
            }
        }

        // Gerar dashboard
        function gerarDashboard() {
            const mapeamento = {
                id: parseInt(document.getElementById('map_id').value),
                titulo: parseInt(document.getElementById('map_titulo').value),
                status: document.getElementById('map_status').value ? parseInt(document.getElementById('map_status').value) : -1,
                categoria: document.getElementById('map_categoria').value ? parseInt(document.getElementById('map_categoria').value) : -1
            };

            console.log('Mapeamento mínimo:', mapeamento);

            const btnGerar = document.getElementById('btnGerar');
            btnGerar.disabled = true;
            btnGerar.innerHTML = '<span class="loading"></span> Gerando...';

            google.script.run
                .withSuccessHandler(function(resultado) {
                    btnGerar.disabled = false;
                    btnGerar.innerHTML = '🚀 Gerar Dashboard';

                    if (resultado.sucesso) {
                        mostrarAlerta('Dashboard gerado com sucesso!', 'success');

                        // Carregar dados
                        dadosDashboard = resultado.dadosDashboard;
                        metricas = resultado.metricas;
                        graficos = resultado.graficos;

                        // Mostrar dashboard
                        setTimeout(() => {
                            mostrarDashboard();
                        }, 1000);

                    } else {
                        mostrarAlerta(resultado.mensagem, 'error');
                    }
                })
                .withFailureHandler(function(erro) {
                    btnGerar.disabled = false;
                    btnGerar.innerHTML = '🚀 Gerar Dashboard';
                    mostrarAlerta('Erro ao gerar dashboard: ' + erro.message, 'error');
                })
                .processarComMapeamentoPersonalizado(dadosBrutos, mapeamento);
        }

        // Mostrar seções
        function mostrarMapeamento() {
            document.getElementById('linkSection').style.display = 'none';
            document.getElementById('mappingSection').style.display = 'block';
            document.getElementById('dashboardSection').style.display = 'none';
        }

        function mostrarDashboard() {
            document.getElementById('linkSection').style.display = 'none';
            document.getElementById('mappingSection').style.display = 'none';
            document.getElementById('dashboardSection').style.display = 'block';

            setTimeout(() => {
                carregarDashboard();
            }, 100);
        }

        // Nova conexão
        function novaConexao() {
            if (confirm('Deseja conectar uma nova planilha?')) {
                location.reload();
            }
        }

        // Mostrar alerta
        function mostrarAlerta(mensagem, tipo) {
            const container = document.getElementById('alertContainer');
            if (!container) {
                alert(mensagem);
                return;
            }

            const alerta = document.createElement('div');
            alerta.className = `alert alert-${tipo}`;
            alerta.textContent = mensagem;

            container.innerHTML = '';
            container.appendChild(alerta);

            setTimeout(() => {
                if (alerta.parentNode) {
                    alerta.remove();
                }
            }, 5000);
        }

        // Carregar dashboard
        function carregarDashboard() {
            console.log('Carregando dashboard mínimo...');

            try {
                // Total de projetos
                if (metricas && metricas.resumoGeral) {
                    const totalEl = document.getElementById('totalProjetos');
                    if (totalEl) totalEl.textContent = metricas.resumoGeral.totalProjetos || 0;
                }

                // Status
                if (metricas && metricas.statusDistribuicao) {
                    carregarStatusMinimo();
                }

                // Categorias
                if (metricas && metricas.categoriaDistribuicao) {
                    carregarCategoriasMinimo();
                }

                // Gráficos
                if (graficos && graficos.status && Object.keys(graficos.status).length > 0) {
                    criarGraficoStatus();
                }

                if (graficos && graficos.categorias && Object.keys(graficos.categorias).length > 0) {
                    criarGraficoCategorias();
                }

                console.log('Dashboard mínimo carregado');

            } catch (error) {
                console.error('Erro ao carregar dashboard:', error);
                mostrarAlerta('Erro ao carregar dashboard: ' + error.message, 'error');
            }
        }

        // Carregar status mínimo
        function carregarStatusMinimo() {
            const container = document.getElementById('statusContainer');
            if (!container) return;

            container.innerHTML = '';

            Object.entries(metricas.statusDistribuicao).forEach(([status, info]) => {
                const item = document.createElement('div');
                item.className = 'status-item';
                item.style.backgroundColor = (graficos.cores && graficos.cores[status]) || '#1976d2';
                item.style.color = '#ffffff';
                item.style.fontSize = '0.9rem';

                item.innerHTML = `
                    <span>${status}</span>
                    <span>${info.quantidade}</span>
                `;

                container.appendChild(item);
            });
        }

        // Carregar categorias mínimo
        function carregarCategoriasMinimo() {
            const container = document.getElementById('categoriaContainer');
            if (!container) return;

            container.innerHTML = '';

            if (metricas.categoriaDistribuicao) {
                Object.entries(metricas.categoriaDistribuicao).forEach(([categoria, info]) => {
                    const item = document.createElement('div');
                    item.className = 'status-item';
                    item.style.backgroundColor = '#4caf50';
                    item.style.color = '#ffffff';
                    item.style.fontSize = '0.9rem';

                    item.innerHTML = `
                        <span>${categoria}</span>
                        <span>${info.quantidade}</span>
                    `;

                    container.appendChild(item);
                });
            }
        }

        // Criar gráfico de status
        function criarGraficoStatus() {
            try {
                const canvas = document.getElementById('statusChart');
                if (!canvas) return;

                const ctx = canvas.getContext('2d');
                const labels = Object.keys(graficos.status || {});
                const data = Object.values(graficos.status || {});
                const colors = labels.map(label => (graficos.cores && graficos.cores[label]) || '#1976d2');

                if (labels.length === 0) return;

                new Chart(ctx, {
                    type: 'pie',
                    data: {
                        labels: labels,
                        datasets: [{
                            data: data,
                            backgroundColor: colors,
                            borderWidth: 2,
                            borderColor: '#ffffff'
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });

            } catch (error) {
                console.error('Erro ao criar gráfico de status:', error);
            }
        }

        // Criar gráfico de categorias
        function criarGraficoCategorias() {
            try {
                const canvas = document.getElementById('categoriaChart');
                if (!canvas) return;

                const ctx = canvas.getContext('2d');
                const labels = Object.keys(graficos.categorias || {});
                const data = Object.values(graficos.categorias || {});

                if (labels.length === 0) return;

                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'Número de Projetos',
                            data: data,
                            backgroundColor: '#1976d2',
                            borderColor: '#1565c0',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });

            } catch (error) {
                console.error('Erro ao criar gráfico de categorias:', error);
            }
        }

        console.log('Script mínimo carregado');
    </script>
</body>
</html>
