// FACEPE Dashboard Application
class FACEPEDashboard {
    constructor() {
        this.currentTab = 'dashboard';
        this.currentPage = 1;
        this.projectsPerPage = 9;
        this.filteredProjects = [];
        this.charts = {};
        
        // Extended dataset based on provided data
        this.data = {
            projetos: [
                {
                    id: 1,
                    titulo: "TerraSense: Monitoramento de umidade em profundidade e em tempo real",
                    ods: "ODS 9 - Indústria, Inovação e Infraestrutura",
                    instituicao: "SENSORIA-SENSORIA SOLUCOES EM ROBOTICA",
                    regiao: "RD 12 - REGIÃO METROPOLITANA",
                    modalidade: "Parceria",
                    resumo: "Sistema inteligente que mede umidade do solo em diferentes profundidades, enviando dados em tempo real para dispositivos móveis."
                },
                {
                    id: 2,
                    titulo: "Solução Orgânica para Produtores Rurais com Sequestro de Carbono",
                    ods: "ODS 15 - Proteger a Vida Terrestre",
                    instituicao: "UNIVASF",
                    regiao: "RD 02 - SERTÃO DO SÃO FRANCISCO",
                    modalidade: "Parceria",
                    resumo: "Solução orgânica líquida inovadora que atua como bioestimulante e condicionador de solo."
                },
                {
                    id: 3,
                    titulo: "Plataforma WallFruits: Otimização Comercial e Gestão Integrada no Agronegócio",
                    ods: "ODS 2 - Fome Zero e Agricultura Sustentável",
                    instituicao: "IFSertãoPE-Petrolina",
                    regiao: "RD 02 - SERTÃO DO SÃO FRANCISCO",
                    modalidade: "Novas Spin-Offs Acadêmicas",
                    resumo: "Plataforma digital que conecta produtores rurais a compradores, serviços de transporte e fornecedores."
                },
                {
                    id: 4,
                    titulo: "Educa Abelha",
                    ods: "ODS 4 - Educação de Qualidade",
                    instituicao: "INCUBATIC",
                    regiao: "RD 12 - REGIÃO METROPOLITANA",
                    modalidade: "Jovem Empreendedora",
                    resumo: "Projeto que ensina a importância das abelhas nativas sem ferrão através de meliponário-escola."
                },
                {
                    id: 5,
                    titulo: "Rastro Rural: Rastreio e monitoramento inteligente",
                    ods: "ODS 12 - Consumo e Produção Responsáveis",
                    instituicao: "UNIVASF",
                    regiao: "RD 02 - SERTÃO DO SÃO FRANCISCO",
                    modalidade: "Parceria",
                    resumo: "Solução para monitoramento em tempo real de temperatura, umidade e emissão de etileno em packinghouses."
                }
            ],
            metricas: {
                totalProjetos: 63,
                totalInstituicoes: 12,
                totalRegioes: 6,
                totalODS: 13,
                distribuicaoModalidades: {
                    "Parceria": 31,
                    "Novas Spin-Offs Acadêmicas": 22,
                    "Jovem Empreendedora": 10
                },
                distribuicaoODS: {
                    "ODS 3 - Saúde e Bem-estar": 25,
                    "ODS 2 - Fome Zero e Agricultura Sustentável": 12,
                    "ODS 9 - Indústria, Inovação e Infraestrutura": 8,
                    "ODS 8 - Trabalho Decente e Crescimento Econômico": 6,
                    "ODS 4 - Educação de Qualidade": 5,
                    "ODS 12 - Consumo e Produção Responsáveis": 4,
                    "ODS 10 - Redução das Desigualdades": 3
                },
                distribuicaoInstituicoes: {
                    "UFPE": 25,
                    "UFRPE": 17,
                    "UNIVASF": 8,
                    "UPE": 5,
                    "ICT-SENAC/PE": 4,
                    "NGPD": 4
                },
                distribuicaoRegional: {
                    "RD 12 - REGIÃO METROPOLITANA": 47,
                    "RD 02 - SERTÃO DO SÃO FRANCISCO": 5,
                    "RD 10 - MATA SUL": 5,
                    "RD 08 - AGRESTE CENTRAL": 3,
                    "RD 09 - AGRESTE SETENTRIONAL": 2,
                    "RD 07 - AGRESTE MERIDIONAL": 1
                }
            }
        };

        // Generate additional sample projects to reach 63 total
        this.generateSampleProjects();
        this.filteredProjects = [...this.data.projetos];
        
        this.init();
    }

    generateSampleProjects() {
        const modalidades = ["Parceria", "Novas Spin-Offs Acadêmicas", "Jovem Empreendedora"];
        const odsList = Object.keys(this.data.metricas.distribuicaoODS);
        const instituicoes = Object.keys(this.data.metricas.distribuicaoInstituicoes);
        const regioes = Object.keys(this.data.metricas.distribuicaoRegional);
        
        const sampleTitles = [
            "Sistema de Monitoramento Inteligente para Agricultura Sustentável",
            "Plataforma de Telemedicina para Comunidades Rurais",
            "Aplicativo de Gestão Hídrica para Pequenos Produtores",
            "Biotecnologia Aplicada ao Tratamento de Resíduos",
            "IoT para Monitoramento Ambiental Urbano",
            "Plataforma de Educação Digital Inclusiva",
            "Sistema de Logística Reversa Inteligente",
            "Tecnologia Assistiva para Pessoas com Deficiência",
            "Marketplace Digital para Economia Solidária",
            "Solução de Energia Renovável Comunitária"
        ];

        for (let i = this.data.projetos.length; i < 63; i++) {
            const randomTitle = sampleTitles[Math.floor(Math.random() * sampleTitles.length)];
            const randomModalidade = modalidades[Math.floor(Math.random() * modalidades.length)];
            const randomODS = odsList[Math.floor(Math.random() * odsList.length)];
            const randomInstituicao = instituicoes[Math.floor(Math.random() * instituicoes.length)];
            const randomRegiao = regioes[Math.floor(Math.random() * regioes.length)];

            this.data.projetos.push({
                id: i + 1,
                titulo: `${randomTitle} ${i + 1}`,
                ods: randomODS,
                instituicao: randomInstituicao,
                regiao: randomRegiao,
                modalidade: randomModalidade,
                resumo: `Projeto inovador que visa contribuir para o desenvolvimento sustentável através de soluções tecnológicas avançadas aplicadas ao setor específico.`
            });
        }
    }

    init() {
        this.setupEventListeners();
        this.initCharts();
        this.renderProjects();
        this.updateDashboard();
    }

    setupEventListeners() {
        // Tab switching
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // Theme toggle
        document.getElementById('themeToggle').addEventListener('click', () => {
            this.toggleTheme();
        });

        // Filters
        document.getElementById('modalidadeFilter').addEventListener('change', () => {
            this.applyFilters();
        });

        document.getElementById('regiaoFilter').addEventListener('change', () => {
            this.applyFilters();
        });

        document.getElementById('searchInput').addEventListener('input', () => {
            this.applyFilters();
        });

        document.getElementById('clearFilters').addEventListener('click', () => {
            this.clearFilters();
        });

        // Modal
        document.getElementById('modalClose').addEventListener('click', () => {
            this.closeModal();
        });

        document.getElementById('projectModal').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) {
                this.closeModal();
            }
        });

        // Export button
        document.getElementById('exportBtn').addEventListener('click', () => {
            this.exportReport();
        });
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');

        this.currentTab = tabName;

        // Refresh charts if switching to dashboard
        if (tabName === 'dashboard') {
            setTimeout(() => this.refreshCharts(), 100);
        }
    }

    toggleTheme() {
        const body = document.body;
        const themeIcon = document.querySelector('.theme-icon');
        
        if (body.dataset.colorScheme === 'dark') {
            body.dataset.colorScheme = 'light';
            themeIcon.textContent = '🌙';
        } else {
            body.dataset.colorScheme = 'dark';
            themeIcon.textContent = '☀️';
        }

        // Refresh charts with new theme
        setTimeout(() => this.refreshCharts(), 100);
    }

    initCharts() {
        this.createModalidadeChart();
        this.createRegionalChart();
        this.createODSChart();
        this.createInstituicaoChart();
    }

    createModalidadeChart() {
        const ctx = document.getElementById('modalidadeChart').getContext('2d');
        const data = this.data.metricas.distribuicaoModalidades;
        
        this.charts.modalidade = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: Object.keys(data),
                datasets: [{
                    data: Object.values(data),
                    backgroundColor: ['#1FB8CD', '#FFC185', '#B4413C'],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed * 100) / total).toFixed(1);
                                return `${context.label}: ${context.parsed} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    createRegionalChart() {
        const ctx = document.getElementById('regionalChart').getContext('2d');
        const data = this.data.metricas.distribuicaoRegional;
        
        this.charts.regional = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: Object.keys(data).map(key => key.replace('RD ', '').replace(' - ', '\n')),
                datasets: [{
                    data: Object.values(data),
                    backgroundColor: ['#1FB8CD', '#FFC185', '#B4413C', '#ECEBD5', '#5D878F', '#DB4545'],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 15,
                            usePointStyle: true,
                            font: {
                                size: 11
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed * 100) / total).toFixed(1);
                                return `${context.label}: ${context.parsed} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    createODSChart() {
        const ctx = document.getElementById('odsChart').getContext('2d');
        const data = this.data.metricas.distribuicaoODS;
        
        this.charts.ods = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: Object.keys(data).map(key => key.replace('ODS ', '').replace(' - ', '\n')),
                datasets: [{
                    label: 'Número de Projetos',
                    data: Object.values(data),
                    backgroundColor: '#1FB8CD',
                    borderColor: '#1FB8CD',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    },
                    x: {
                        ticks: {
                            maxRotation: 45,
                            minRotation: 45,
                            font: {
                                size: 10
                            }
                        }
                    }
                }
            }
        });
    }

    createInstituicaoChart() {
        const ctx = document.getElementById('instituicaoChart').getContext('2d');
        const data = this.data.metricas.distribuicaoInstituicoes;
        
        this.charts.instituicao = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: Object.keys(data),
                datasets: [{
                    label: 'Número de Projetos',
                    data: Object.values(data),
                    backgroundColor: '#FFC185',
                    borderColor: '#FFC185',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    },
                    x: {
                        ticks: {
                            maxRotation: 45,
                            minRotation: 45
                        }
                    }
                }
            }
        });
    }

    refreshCharts() {
        Object.values(this.charts).forEach(chart => {
            if (chart && chart.update) {
                chart.update();
            }
        });
    }

    applyFilters() {
        const modalidadeFilter = document.getElementById('modalidadeFilter').value;
        const regiaoFilter = document.getElementById('regiaoFilter').value;
        const searchFilter = document.getElementById('searchInput').value.toLowerCase();

        this.filteredProjects = this.data.projetos.filter(projeto => {
            const matchesModalidade = !modalidadeFilter || projeto.modalidade === modalidadeFilter;
            const matchesRegiao = !regiaoFilter || projeto.regiao === regiaoFilter;
            const matchesSearch = !searchFilter || 
                projeto.titulo.toLowerCase().includes(searchFilter) ||
                projeto.instituicao.toLowerCase().includes(searchFilter) ||
                projeto.ods.toLowerCase().includes(searchFilter);

            return matchesModalidade && matchesRegiao && matchesSearch;
        });

        this.currentPage = 1;
        this.renderProjects();
    }

    clearFilters() {
        document.getElementById('modalidadeFilter').value = '';
        document.getElementById('regiaoFilter').value = '';
        document.getElementById('searchInput').value = '';
        
        this.filteredProjects = [...this.data.projetos];
        this.currentPage = 1;
        this.renderProjects();
    }

    renderProjects() {
        const grid = document.getElementById('projectsGrid');
        const startIndex = (this.currentPage - 1) * this.projectsPerPage;
        const endIndex = startIndex + this.projectsPerPage;
        const projectsToShow = this.filteredProjects.slice(startIndex, endIndex);

        if (projectsToShow.length === 0) {
            grid.innerHTML = `
                <div class="empty-state">
                    <div class="empty-state__icon">🔍</div>
                    <div class="empty-state__message">Nenhum projeto encontrado</div>
                    <div class="empty-state__description">Tente ajustar os filtros para encontrar projetos</div>
                </div>
            `;
        } else {
            grid.innerHTML = projectsToShow.map(projeto => this.createProjectCard(projeto)).join('');
        }

        this.renderPagination();
    }

    createProjectCard(projeto) {
        const badgeClass = this.getModalidadeBadgeClass(projeto.modalidade);
        
        return `
            <div class="project-card" onclick="dashboard.openModal(${projeto.id})">
                <div class="project-card__header">
                    <h3 class="project-card__title">${projeto.titulo}</h3>
                    <span class="project-card__badge ${badgeClass}">${projeto.modalidade}</span>
                </div>
                <div class="project-card__info">
                    <div class="project-card__detail">
                        <span class="project-card__icon">🏛️</span>
                        <span>${projeto.instituicao}</span>
                    </div>
                    <div class="project-card__detail">
                        <span class="project-card__icon">🎯</span>
                        <span>${projeto.ods}</span>
                    </div>
                    <div class="project-card__detail">
                        <span class="project-card__icon">📍</span>
                        <span>${projeto.regiao.replace('RD ', '').replace(' - ', ' ')}</span>
                    </div>
                </div>
            </div>
        `;
    }

    getModalidadeBadgeClass(modalidade) {
        switch (modalidade) {
            case 'Parceria':
                return 'project-card__badge--parceria';
            case 'Novas Spin-Offs Acadêmicas':
                return 'project-card__badge--spinoff';
            case 'Jovem Empreendedora':
                return 'project-card__badge--jovem';
            default:
                return 'project-card__badge--parceria';
        }
    }

    renderPagination() {
        const pagination = document.getElementById('pagination');
        const totalPages = Math.ceil(this.filteredProjects.length / this.projectsPerPage);
        
        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }

        let paginationHTML = '';
        
        // Previous button
        paginationHTML += `
            <button class="pagination-btn" ${this.currentPage === 1 ? 'disabled' : ''} 
                    onclick="dashboard.changePage(${this.currentPage - 1})">
                « Anterior
            </button>
        `;

        // Page numbers
        for (let i = 1; i <= Math.min(totalPages, 5); i++) {
            const pageNum = this.currentPage <= 3 ? i : 
                           this.currentPage >= totalPages - 2 ? totalPages - 5 + i :
                           this.currentPage - 3 + i;
            
            if (pageNum > 0 && pageNum <= totalPages) {
                paginationHTML += `
                    <button class="pagination-btn ${pageNum === this.currentPage ? 'active' : ''}"
                            onclick="dashboard.changePage(${pageNum})">
                        ${pageNum}
                    </button>
                `;
            }
        }

        // Next button
        paginationHTML += `
            <button class="pagination-btn" ${this.currentPage === totalPages ? 'disabled' : ''} 
                    onclick="dashboard.changePage(${this.currentPage + 1})">
                Próxima »
            </button>
        `;

        pagination.innerHTML = paginationHTML;
    }

    changePage(page) {
        const totalPages = Math.ceil(this.filteredProjects.length / this.projectsPerPage);
        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
            this.renderProjects();
        }
    }

    openModal(projectId) {
        const projeto = this.data.projetos.find(p => p.id === projectId);
        if (!projeto) return;

        document.getElementById('modalTitle').textContent = projeto.titulo;
        document.getElementById('modalBody').innerHTML = `
            <div class="modal-detail">
                <span class="modal-detail__label">Modalidade:</span>
                <span class="modal-detail__value">${projeto.modalidade}</span>
            </div>
            <div class="modal-detail">
                <span class="modal-detail__label">Instituição:</span>
                <span class="modal-detail__value">${projeto.instituicao}</span>
            </div>
            <div class="modal-detail">
                <span class="modal-detail__label">ODS:</span>
                <span class="modal-detail__value">${projeto.ods}</span>
            </div>
            <div class="modal-detail">
                <span class="modal-detail__label">Região:</span>
                <span class="modal-detail__value">${projeto.regiao}</span>
            </div>
            <div class="modal-detail">
                <span class="modal-detail__label">Resumo:</span>
                <span class="modal-detail__value">${projeto.resumo}</span>
            </div>
        `;

        document.getElementById('projectModal').classList.add('active');
    }

    closeModal() {
        document.getElementById('projectModal').classList.remove('active');
    }

    exportReport() {
        // Simulate export functionality
        const data = {
            timestamp: new Date().toLocaleString('pt-BR'),
            totalProjetos: this.filteredProjects.length,
            projetos: this.filteredProjects.map(p => ({
                titulo: p.titulo,
                modalidade: p.modalidade,
                instituicao: p.instituicao,
                ods: p.ods,
                regiao: p.regiao
            }))
        };

        // Create downloadable content
        const jsonStr = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonStr], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = `relatorio-facepe-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        URL.revokeObjectURL(url);

        // Show success message
        alert('Relatório exportado com sucesso!');
    }

    updateDashboard() {
        // This method could be used to update dashboard metrics in real-time
        // For now, it's a placeholder for future enhancements
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new FACEPEDashboard();
});

// Handle responsive sidebar on mobile
window.addEventListener('resize', () => {
    if (window.innerWidth > 768) {
        document.querySelector('.sidebar').style.display = 'block';
    }
});