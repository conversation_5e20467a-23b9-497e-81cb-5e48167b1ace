/**
 * MÓDULO DE ABA DE MÉTRICAS
 * Funções para criar e gerenciar a aba de métricas detalhadas
 */

// ==================== CRIAÇÃO DA ABA DE MÉTRICAS ====================
/**
 * Cria a aba de métricas com dados estruturados
 */
function criarAbaMetricas(planilha, metricas) {
  try {
    console.log('📋 Criando aba de métricas...');
    
    // Criar/obter aba de métricas
    let abaMetricas = planilha.getSheetByName(CONFIG.ABA_METRICAS);
    if (!abaMetricas) {
      abaMetricas = planilha.insertSheet(CONFIG.ABA_METRICAS);
    }
    
    // Limpar conteúdo existente
    abaMetricas.clear();
    
    // Configurar layout
    configurarLayoutMetricas(abaMetricas);
    
    // Inserir seções de métricas
    let linhaAtual = 3;
    
    linhaAtual = inserirResumoGeral(abaMetricas, metricas.resumoGeral, linhaAtual);
    linhaAtual = inserirDistribuicaoStatus(abaMetricas, metricas.statusDistribuicao, linhaAtual);
    linhaAtual = inserirAnaliseOrcamento(abaMetricas, metricas.orcamentoAnalise, linhaAtual);
    linhaAtual = inserirPerformanceAvaliadores(abaMetricas, metricas.avaliadoresPerformance, linhaAtual);
    linhaAtual = inserirDistribuicaoCategoria(abaMetricas, metricas.categoriaDistribuicao, linhaAtual);
    linhaAtual = inserirPrazosCriticos(abaMetricas, metricas.prazosCriticos, linhaAtual);
    linhaAtual = inserirTendencias(abaMetricas, metricas.tendencias, linhaAtual);
    
    // Aplicar formatação final
    aplicarFormatacaoMetricas(abaMetricas);
    
    console.log('✅ Aba de métricas criada com sucesso');
    
  } catch (error) {
    console.error('❌ Erro na criação da aba de métricas:', error);
    throw error;
  }
}

// ==================== CONFIGURAÇÃO DE LAYOUT ====================
/**
 * Configura o layout básico da aba de métricas
 */
function configurarLayoutMetricas(aba) {
  // Definir larguras das colunas
  aba.setColumnWidth(1, 250);  // Coluna A - Descrição
  aba.setColumnWidth(2, 150);  // Coluna B - Valor
  aba.setColumnWidth(3, 100);  // Coluna C - Unidade/Tipo
  aba.setColumnWidth(4, 50);   // Coluna D - Espaçador
  aba.setColumnWidth(5, 200);  // Coluna E - Descrição adicional
  aba.setColumnWidth(6, 150);  // Coluna F - Valor adicional
  
  // Título principal
  aba.getRange('A1:F1').merge();
  aba.getRange('A1').setValue('📊 MÉTRICAS DETALHADAS DO PORTFÓLIO');
  aba.getRange('A1')
    .setFontSize(18)
    .setFontWeight('bold')
    .setHorizontalAlignment('center')
    .setBackground('#1f4e79')
    .setFontColor('#ffffff');
    
  // Data de cálculo
  aba.getRange('A2:F2').merge();
  aba.getRange('A2').setValue(`Calculado em: ${new Date().toLocaleString('pt-BR')}`);
  aba.getRange('A2')
    .setFontSize(10)
    .setHorizontalAlignment('center')
    .setBackground('#f0f0f0')
    .setFontStyle('italic');
}

// ==================== SEÇÕES DE MÉTRICAS ====================
/**
 * Insere resumo geral
 */
function inserirResumoGeral(aba, resumo, linha) {
  // Título da seção
  aba.getRange(linha, 1).setValue('📈 RESUMO GERAL');
  aba.getRange(linha, 1, 1, 3)
    .setFontWeight('bold')
    .setFontSize(14)
    .setBackground('#e3f2fd');
  
  linha++;
  
  // Dados do resumo
  const dados = [
    ['Total de Projetos', resumo.totalProjetos, 'projetos'],
    ['Orçamento Total', resumo.orcamentoTotal, 'R$'],
    ['Progresso Médio', resumo.progressoMedio, '%']
  ];
  
  aba.getRange(linha, 1, dados.length, 3).setValues(dados);
  
  // Formatação
  aba.getRange(linha, 1, dados.length, 1).setFontWeight('bold');
  aba.getRange(linha, 2, dados.length, 1)
    .setHorizontalAlignment('right')
    .setNumberFormat('#,##0.00');
  
  return linha + dados.length + 2;
}

/**
 * Insere distribuição por status
 */
function inserirDistribuicaoStatus(aba, distribuicao, linha) {
  // Título da seção
  aba.getRange(linha, 1).setValue('🎯 DISTRIBUIÇÃO POR STATUS');
  aba.getRange(linha, 1, 1, 3)
    .setFontWeight('bold')
    .setFontSize(14)
    .setBackground('#e8f5e8');
  
  linha++;
  
  // Cabeçalho
  const cabecalho = [['Status', 'Quantidade', 'Percentual']];
  aba.getRange(linha, 1, 1, 3).setValues(cabecalho);
  aba.getRange(linha, 1, 1, 3)
    .setFontWeight('bold')
    .setBackground('#f5f5f5');
  
  linha++;
  
  // Dados da distribuição
  const dados = [];
  Object.entries(distribuicao).forEach(([status, info]) => {
    dados.push([status, info.quantidade, info.percentual + '%']);
  });
  
  if (dados.length > 0) {
    aba.getRange(linha, 1, dados.length, 3).setValues(dados);
    
    // Aplicar cores por status
    for (let i = 0; i < dados.length; i++) {
      const status = dados[i][0];
      if (CONFIG.CORES[status]) {
        aba.getRange(linha + i, 1, 1, 3)
          .setBackground(CONFIG.CORES[status])
          .setFontColor('#ffffff');
      }
    }
  }
  
  return linha + dados.length + 2;
}

/**
 * Insere análise de orçamento
 */
function inserirAnaliseOrcamento(aba, analise, linha) {
  // Título da seção
  aba.getRange(linha, 1).setValue('💰 ANÁLISE DE ORÇAMENTO');
  aba.getRange(linha, 1, 1, 3)
    .setFontWeight('bold')
    .setFontSize(14)
    .setBackground('#fff3e0');
  
  linha++;
  
  // Dados da análise
  const dados = [
    ['Orçamento Total', analise.total, 'R$'],
    ['Orçamento Médio', analise.medio, 'R$'],
    ['Maior Orçamento', analise.maximo, 'R$'],
    ['Menor Orçamento', analise.minimo, 'R$']
  ];
  
  aba.getRange(linha, 1, dados.length, 3).setValues(dados);
  
  // Formatação
  aba.getRange(linha, 1, dados.length, 1).setFontWeight('bold');
  aba.getRange(linha, 2, dados.length, 1)
    .setHorizontalAlignment('right')
    .setNumberFormat('#,##0');
  
  return linha + dados.length + 2;
}

/**
 * Insere performance dos avaliadores
 */
function inserirPerformanceAvaliadores(aba, performance, linha) {
  // Título da seção
  aba.getRange(linha, 1).setValue('👥 PERFORMANCE DOS AVALIADORES');
  aba.getRange(linha, 1, 1, 6)
    .setFontWeight('bold')
    .setFontSize(14)
    .setBackground('#f3e5f5');
  
  linha++;
  
  // Cabeçalho
  const cabecalho = [['Avaliador', 'Total Proj.', 'Concluídos', 'Em Andamento', 'Taxa Conclusão', 'Orçamento Total']];
  aba.getRange(linha, 1, 1, 6).setValues(cabecalho);
  aba.getRange(linha, 1, 1, 6)
    .setFontWeight('bold')
    .setBackground('#f5f5f5');
  
  linha++;
  
  // Dados dos avaliadores
  const dados = [];
  Object.entries(performance).forEach(([avaliador, perf]) => {
    dados.push([
      avaliador,
      perf.totalProjetos,
      perf.concluidos,
      perf.emAndamento,
      perf.taxaConclusao + '%',
      'R$ ' + perf.orcamentoTotal.toLocaleString('pt-BR')
    ]);
  });
  
  if (dados.length > 0) {
    aba.getRange(linha, 1, dados.length, 6).setValues(dados);
    
    // Formatação
    aba.getRange(linha, 2, dados.length, 3).setHorizontalAlignment('center');
    aba.getRange(linha, 5, dados.length, 1).setHorizontalAlignment('center');
    aba.getRange(linha, 6, dados.length, 1).setHorizontalAlignment('right');
  }
  
  return linha + dados.length + 2;
}

/**
 * Insere distribuição por categoria
 */
function inserirDistribuicaoCategoria(aba, distribuicao, linha) {
  // Título da seção
  aba.getRange(linha, 1).setValue('📂 DISTRIBUIÇÃO POR CATEGORIA');
  aba.getRange(linha, 1, 1, 4)
    .setFontWeight('bold')
    .setFontSize(14)
    .setBackground('#e0f2f1');
  
  linha++;
  
  // Cabeçalho
  const cabecalho = [['Categoria', 'Quantidade', 'Orçamento Total', 'Progresso Médio']];
  aba.getRange(linha, 1, 1, 4).setValues(cabecalho);
  aba.getRange(linha, 1, 1, 4)
    .setFontWeight('bold')
    .setBackground('#f5f5f5');
  
  linha++;
  
  // Dados das categorias
  const dados = [];
  Object.entries(distribuicao).forEach(([categoria, info]) => {
    dados.push([
      categoria,
      info.quantidade,
      'R$ ' + info.orcamentoTotal.toLocaleString('pt-BR'),
      info.progressoMedio + '%'
    ]);
  });
  
  if (dados.length > 0) {
    aba.getRange(linha, 1, dados.length, 4).setValues(dados);
    
    // Formatação
    aba.getRange(linha, 2, dados.length, 1).setHorizontalAlignment('center');
    aba.getRange(linha, 3, dados.length, 1).setHorizontalAlignment('right');
    aba.getRange(linha, 4, dados.length, 1).setHorizontalAlignment('center');
  }
  
  return linha + dados.length + 2;
}

/**
 * Insere projetos com prazos críticos
 */
function inserirPrazosCriticos(aba, prazosCriticos, linha) {
  // Título da seção
  aba.getRange(linha, 1).setValue('⚠️ PROJETOS COM PRAZOS CRÍTICOS');
  aba.getRange(linha, 1, 1, 6)
    .setFontWeight('bold')
    .setFontSize(14)
    .setBackground('#ffebee');
  
  linha++;
  
  if (prazosCriticos.length === 0) {
    aba.getRange(linha, 1).setValue('✅ Nenhum projeto com prazo crítico');
    aba.getRange(linha, 1).setFontColor('#4caf50');
    return linha + 2;
  }
  
  // Cabeçalho
  const cabecalho = [['ID', 'Título', 'Responsável', 'Data Fim', 'Dias Restantes', 'Progresso']];
  aba.getRange(linha, 1, 1, 6).setValues(cabecalho);
  aba.getRange(linha, 1, 1, 6)
    .setFontWeight('bold')
    .setBackground('#f5f5f5');
  
  linha++;
  
  // Dados dos projetos críticos
  const dados = [];
  prazosCriticos.forEach(projeto => {
    dados.push([
      projeto.id,
      projeto.titulo,
      projeto.responsavel,
      projeto.dataFim.toLocaleDateString('pt-BR'),
      projeto.diasRestantes,
      projeto.progresso + '%'
    ]);
  });
  
  aba.getRange(linha, 1, dados.length, 6).setValues(dados);
  
  // Formatação condicional para dias restantes
  const rangeDias = aba.getRange(linha, 5, dados.length, 1);
  
  // Vermelho para < 7 dias
  const regraCritica = SpreadsheetApp.newConditionalFormatRule()
    .whenNumberLessThan(7)
    .setBackground('#ffcdd2')
    .setRanges([rangeDias])
    .build();
  
  // Amarelo para 7-15 dias
  const regraAlerta = SpreadsheetApp.newConditionalFormatRule()
    .whenNumberBetween(7, 15)
    .setBackground('#fff9c4')
    .setRanges([rangeDias])
    .build();
  
  aba.setConditionalFormatRules([regraCritica, regraAlerta]);
  
  return linha + dados.length + 2;
}

/**
 * Insere análise de tendências
 */
function inserirTendencias(aba, tendencias, linha) {
  // Título da seção
  aba.getRange(linha, 1).setValue('📈 TENDÊNCIAS TEMPORAIS');
  aba.getRange(linha, 1, 1, 6)
    .setFontWeight('bold')
    .setFontSize(14)
    .setBackground('#e1f5fe');
  
  linha++;
  
  // Projetos por mês
  aba.getRange(linha, 1).setValue('Novos Projetos por Mês:');
  aba.getRange(linha, 1).setFontWeight('bold');
  linha++;
  
  const mesesOrdenados = Object.keys(tendencias.projetosPorMes).sort();
  mesesOrdenados.forEach(mes => {
    aba.getRange(linha, 1).setValue(mes);
    aba.getRange(linha, 2).setValue(tendencias.projetosPorMes[mes]);
    linha++;
  });
  
  return linha + 2;
}

// ==================== FORMATAÇÃO FINAL ====================
/**
 * Aplica formatação final à aba de métricas
 */
function aplicarFormatacaoMetricas(aba) {
  // Ajustar altura das linhas
  aba.setRowHeight(1, 40);
  aba.setRowHeight(2, 25);
  
  // Congelar primeiras linhas
  aba.setFrozenRows(2);
  
  // Bordas para toda a área de dados
  const ultimaLinha = aba.getLastRow();
  const ultimaColuna = aba.getLastColumn();
  
  if (ultimaLinha > 2 && ultimaColuna > 0) {
    aba.getRange(3, 1, ultimaLinha - 2, ultimaColuna)
      .setBorder(true, true, true, true, true, true);
  }
}
