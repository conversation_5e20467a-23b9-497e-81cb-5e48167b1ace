/**
 * MÓDULO DE GRÁFICOS
 * Funções para criar gráficos interativos no dashboard
 */

// ==================== CRIAÇÃO DE GRÁFICOS PRINCIPAIS ====================
/**
 * Cria todos os gráficos do dashboard
 */
function criarGraficos() {
  try {
    console.log('📊 Criando gráficos...');
    
    const planilha = SpreadsheetApp.getActiveSpreadsheet();
    
    // Criar/obter aba de gráficos
    let abaGraficos = planilha.getSheetByName(CONFIG.ABA_GRAFICOS);
    if (!abaGraficos) {
      abaGraficos = planilha.insertSheet(CONFIG.ABA_GRAFICOS);
    }
    
    // Limpar gráficos existentes
    const graficos = abaGraficos.getCharts();
    graficos.forEach(grafico => abaGraficos.removeChart(grafico));
    abaGraficos.clear();
    
    // Preparar dados para gráficos
    prepararDadosGraficos(abaGraficos);
    
    // Criar gráficos específicos
    criarGraficoPizza(abaGraficos);
    criarGraficoBarras(abaGraficos);
    criarGraficoLinha(abaGraficos);
    criarGraficoOrcamento(abaGraficos);
    
    // Configurar layout da aba
    configurarLayoutGraficos(abaGraficos);
    
    console.log('✅ Gráficos criados com sucesso');
    
  } catch (error) {
    console.error('❌ Erro na criação de gráficos:', error);
    throw error;
  }
}

// ==================== PREPARAÇÃO DE DADOS ====================
/**
 * Prepara dados estruturados para os gráficos
 */
function prepararDadosGraficos(aba) {
  const planilha = SpreadsheetApp.getActiveSpreadsheet();
  const abaDados = planilha.getSheetByName(CONFIG.ABA_DADOS);
  
  if (!abaDados) return;
  
  const dados = abaDados.getDataRange().getValues();
  
  // Título da aba
  aba.getRange('A1').setValue('📊 GRÁFICOS DO DASHBOARD');
  aba.getRange('A1')
    .setFontSize(16)
    .setFontWeight('bold')
    .setBackground('#1f4e79')
    .setFontColor('#ffffff');
  
  // ===== DADOS PARA GRÁFICO DE PIZZA (STATUS) =====
  aba.getRange('A3').setValue('Dados - Status dos Projetos');
  aba.getRange('A3').setFontWeight('bold');
  
  const statusCount = {};
  for (let i = 1; i < dados.length; i++) {
    const status = dados[i][3] || 'Não Definido';
    statusCount[status] = (statusCount[status] || 0) + 1;
  }
  
  const dadosStatus = [['Status', 'Quantidade']];
  Object.entries(statusCount).forEach(([status, count]) => {
    dadosStatus.push([status, count]);
  });
  
  aba.getRange(4, 1, dadosStatus.length, 2).setValues(dadosStatus);
  
  // ===== DADOS PARA GRÁFICO DE BARRAS (CATEGORIAS) =====
  const linhaCategoria = dadosStatus.length + 6;
  aba.getRange(linhaCategoria, 1).setValue('Dados - Projetos por Categoria');
  aba.getRange(linhaCategoria, 1).setFontWeight('bold');
  
  const categoriaCount = {};
  for (let i = 1; i < dados.length; i++) {
    const categoria = dados[i][8] || 'Não Definido';
    categoriaCount[categoria] = (categoriaCount[categoria] || 0) + 1;
  }
  
  const dadosCategoria = [['Categoria', 'Projetos']];
  Object.entries(categoriaCount).forEach(([categoria, count]) => {
    dadosCategoria.push([categoria, count]);
  });
  
  aba.getRange(linhaCategoria + 1, 1, dadosCategoria.length, 2).setValues(dadosCategoria);
  
  // ===== DADOS PARA GRÁFICO DE ORÇAMENTO =====
  const linhaOrcamento = linhaCategoria + dadosCategoria.length + 3;
  aba.getRange(linhaOrcamento, 1).setValue('Dados - Orçamento por Categoria');
  aba.getRange(linhaOrcamento, 1).setFontWeight('bold');
  
  const orcamentoCategoria = {};
  for (let i = 1; i < dados.length; i++) {
    const categoria = dados[i][8] || 'Não Definido';
    const orcamento = parseFloat(dados[i][6]) || 0;
    orcamentoCategoria[categoria] = (orcamentoCategoria[categoria] || 0) + orcamento;
  }
  
  const dadosOrcamento = [['Categoria', 'Orçamento (R$)']];
  Object.entries(orcamentoCategoria).forEach(([categoria, orcamento]) => {
    dadosOrcamento.push([categoria, orcamento]);
  });
  
  aba.getRange(linhaOrcamento + 1, 1, dadosOrcamento.length, 2).setValues(dadosOrcamento);
  
  // ===== DADOS PARA GRÁFICO DE LINHA (TENDÊNCIA TEMPORAL) =====
  const linhaTempo = linhaOrcamento + dadosOrcamento.length + 3;
  aba.getRange(linhaTempo, 1).setValue('Dados - Projetos por Mês');
  aba.getRange(linhaTempo, 1).setFontWeight('bold');
  
  const projetosPorMes = {};
  for (let i = 1; i < dados.length; i++) {
    const dataInicio = new Date(dados[i][4]);
    if (dataInicio && !isNaN(dataInicio)) {
      const mes = `${dataInicio.getFullYear()}-${(dataInicio.getMonth() + 1).toString().padStart(2, '0')}`;
      projetosPorMes[mes] = (projetosPorMes[mes] || 0) + 1;
    }
  }
  
  const dadosTempo = [['Mês', 'Novos Projetos']];
  Object.entries(projetosPorMes)
    .sort((a, b) => a[0].localeCompare(b[0]))
    .forEach(([mes, count]) => {
      dadosTempo.push([mes, count]);
    });
  
  aba.getRange(linhaTempo + 1, 1, dadosTempo.length, 2).setValues(dadosTempo);
  
  // Armazenar posições para referência
  aba.getRange('Z1').setValue(JSON.stringify({
    statusRange: `A4:B${3 + dadosStatus.length}`,
    categoriaRange: `A${linhaCategoria + 1}:B${linhaCategoria + dadosCategoria.length}`,
    orcamentoRange: `A${linhaOrcamento + 1}:B${linhaOrcamento + dadosOrcamento.length}`,
    tempoRange: `A${linhaTempo + 1}:B${linhaTempo + dadosTempo.length}`
  }));
}

// ==================== GRÁFICOS ESPECÍFICOS ====================
/**
 * Cria gráfico de pizza para distribuição de status
 */
function criarGraficoPizza(aba) {
  try {
    const ranges = JSON.parse(aba.getRange('Z1').getValue());
    const dataRange = aba.getRange(ranges.statusRange);
    
    const grafico = aba.newChart()
      .setChartType(Charts.ChartType.PIE)
      .addRange(dataRange)
      .setPosition(2, 4, 0, 0)
      .setOption('title', 'Distribuição por Status')
      .setOption('titleTextStyle', {
        fontSize: 14,
        bold: true
      })
      .setOption('pieSliceTextStyle', {
        fontSize: 10
      })
      .setOption('legend', {
        position: 'right',
        textStyle: { fontSize: 10 }
      })
      .setOption('width', 400)
      .setOption('height', 300)
      .setOption('colors', Object.values(CONFIG.CORES))
      .build();
    
    aba.insertChart(grafico);
    
  } catch (error) {
    console.error('Erro no gráfico de pizza:', error);
  }
}

/**
 * Cria gráfico de barras para categorias
 */
function criarGraficoBarras(aba) {
  try {
    const ranges = JSON.parse(aba.getRange('Z1').getValue());
    const dataRange = aba.getRange(ranges.categoriaRange);
    
    const grafico = aba.newChart()
      .setChartType(Charts.ChartType.COLUMN)
      .addRange(dataRange)
      .setPosition(2, 8, 0, 0)
      .setOption('title', 'Projetos por Categoria')
      .setOption('titleTextStyle', {
        fontSize: 14,
        bold: true
      })
      .setOption('hAxis', {
        title: 'Categoria',
        titleTextStyle: { fontSize: 12 },
        textStyle: { fontSize: 9 }
      })
      .setOption('vAxis', {
        title: 'Número de Projetos',
        titleTextStyle: { fontSize: 12 }
      })
      .setOption('legend', { position: 'none' })
      .setOption('width', 400)
      .setOption('height', 300)
      .setOption('colors', ['#2196F3'])
      .build();
    
    aba.insertChart(grafico);
    
  } catch (error) {
    console.error('Erro no gráfico de barras:', error);
  }
}

/**
 * Cria gráfico de linha para tendência temporal
 */
function criarGraficoLinha(aba) {
  try {
    const ranges = JSON.parse(aba.getRange('Z1').getValue());
    const dataRange = aba.getRange(ranges.tempoRange);
    
    const grafico = aba.newChart()
      .setChartType(Charts.ChartType.LINE)
      .addRange(dataRange)
      .setPosition(18, 4, 0, 0)
      .setOption('title', 'Tendência de Novos Projetos')
      .setOption('titleTextStyle', {
        fontSize: 14,
        bold: true
      })
      .setOption('hAxis', {
        title: 'Período',
        titleTextStyle: { fontSize: 12 },
        textStyle: { fontSize: 9 }
      })
      .setOption('vAxis', {
        title: 'Novos Projetos',
        titleTextStyle: { fontSize: 12 }
      })
      .setOption('legend', { position: 'none' })
      .setOption('width', 400)
      .setOption('height', 300)
      .setOption('colors', ['#FF9800'])
      .setOption('curveType', 'function')
      .setOption('pointSize', 5)
      .build();
    
    aba.insertChart(grafico);
    
  } catch (error) {
    console.error('Erro no gráfico de linha:', error);
  }
}

/**
 * Cria gráfico de orçamento por categoria
 */
function criarGraficoOrcamento(aba) {
  try {
    const ranges = JSON.parse(aba.getRange('Z1').getValue());
    const dataRange = aba.getRange(ranges.orcamentoRange);
    
    const grafico = aba.newChart()
      .setChartType(Charts.ChartType.BAR)
      .addRange(dataRange)
      .setPosition(18, 8, 0, 0)
      .setOption('title', 'Orçamento por Categoria')
      .setOption('titleTextStyle', {
        fontSize: 14,
        bold: true
      })
      .setOption('hAxis', {
        title: 'Orçamento (R$)',
        titleTextStyle: { fontSize: 12 },
        format: 'currency'
      })
      .setOption('vAxis', {
        title: 'Categoria',
        titleTextStyle: { fontSize: 12 },
        textStyle: { fontSize: 9 }
      })
      .setOption('legend', { position: 'none' })
      .setOption('width', 400)
      .setOption('height', 300)
      .setOption('colors', ['#4CAF50'])
      .build();
    
    aba.insertChart(grafico);
    
  } catch (error) {
    console.error('Erro no gráfico de orçamento:', error);
  }
}

// ==================== CONFIGURAÇÃO DE LAYOUT ====================
/**
 * Configura o layout da aba de gráficos
 */
function configurarLayoutGraficos(aba) {
  // Ajustar larguras das colunas
  aba.setColumnWidth(1, 150);
  aba.setColumnWidth(2, 120);
  aba.setColumnWidth(3, 50);
  
  // Formatar cabeçalhos
  const cabecalhos = aba.getRange('A1:Z1');
  cabecalhos.setBackground('#1f4e79').setFontColor('#ffffff');
  
  // Ocultar coluna de dados auxiliares
  aba.hideColumns(26); // Coluna Z
  
  // Congelar primeira linha
  aba.setFrozenRows(1);
}

// ==================== FUNÇÕES AUXILIARES ====================
/**
 * Atualiza apenas os gráficos sem recriar dados
 */
function atualizarGraficos() {
  try {
    const planilha = SpreadsheetApp.getActiveSpreadsheet();
    const aba = planilha.getSheetByName(CONFIG.ABA_GRAFICOS);
    
    if (!aba) {
      criarGraficos();
      return;
    }
    
    // Remover gráficos existentes
    const graficos = aba.getCharts();
    graficos.forEach(grafico => aba.removeChart(grafico));
    
    // Recriar apenas os gráficos
    criarGraficoPizza(aba);
    criarGraficoBarras(aba);
    criarGraficoLinha(aba);
    criarGraficoOrcamento(aba);
    
    console.log('✅ Gráficos atualizados');
    
  } catch (error) {
    console.error('❌ Erro na atualização de gráficos:', error);
  }
}
