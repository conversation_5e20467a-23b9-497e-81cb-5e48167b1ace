/**
 * MÓDULO DE MÉTRICAS E CÁLCULOS
 * Funções para calcular KPIs e métricas do dashboard
 */

// ==================== CÁLCULO DE MÉTRICAS PRINCIPAIS ====================
/**
 * Calcula todas as métricas e atualiza a aba de métricas
 */
function calcularMetricas() {
  try {
    console.log('📊 Calculando métricas...');
    
    const planilha = SpreadsheetApp.getActiveSpreadsheet();
    const abaDados = planilha.getSheetByName(CONFIG.ABA_DADOS);
    
    if (!abaDados) {
      throw new Error('Aba de dados não encontrada. Execute primeiro a importação.');
    }
    
    // Obter dados
    const dados = obterDadosProjetos(abaDados);
    
    if (dados.length === 0) {
      throw new Error('Nenhum dado encontrado para calcular métricas.');
    }
    
    // Calcular métricas
    const metricas = {
      resumoGeral: calcularResumoGeral(dados),
      statusDistribuicao: calcularDistribuicaoStatus(dados),
      orcamentoAnalise: calcularAnaliseOrcamento(dados),
      progressoMedio: calcularProgressoMedio(dados),
      avaliadoresPerformance: calcularPerformanceAvaliadores(dados),
      categoriaDistribuicao: calcularDistribuicaoCategoria(dados),
      prazosCriticos: identificarPrazosCriticos(dados),
      tendencias: calcularTendencias(dados)
    };
    
    // Criar/atualizar aba de métricas
    criarAbaMetricas(planilha, metricas);
    
    console.log('✅ Métricas calculadas com sucesso');
    
  } catch (error) {
    console.error('❌ Erro no cálculo de métricas:', error);
    throw error;
  }
}

// ==================== OBTENÇÃO DE DADOS ====================
/**
 * Obtém dados estruturados da aba de projetos
 */
function obterDadosProjetos(aba) {
  const dados = aba.getDataRange().getValues();
  
  if (dados.length <= 1) return [];
  
  const cabecalho = dados[0];
  const projetos = [];
  
  // Mapear índices das colunas
  const indices = {
    id: cabecalho.indexOf('ID'),
    titulo: cabecalho.indexOf('Título'),
    responsavel: cabecalho.indexOf('Responsável'),
    status: cabecalho.indexOf('Status'),
    dataInicio: cabecalho.indexOf('Data_Início'),
    dataFim: cabecalho.indexOf('Data_Fim'),
    orcamento: cabecalho.indexOf('Orçamento'),
    progresso: cabecalho.indexOf('Progresso'),
    categoria: cabecalho.indexOf('Categoria'),
    avaliador: cabecalho.indexOf('Avaliador')
  };
  
  // Processar cada linha de dados
  for (let i = 1; i < dados.length; i++) {
    const linha = dados[i];
    
    const projeto = {
      id: linha[indices.id] || '',
      titulo: linha[indices.titulo] || '',
      responsavel: linha[indices.responsavel] || '',
      status: linha[indices.status] || '',
      dataInicio: parseData(linha[indices.dataInicio]),
      dataFim: parseData(linha[indices.dataFim]),
      orcamento: parseFloat(linha[indices.orcamento]) || 0,
      progresso: parseFloat(linha[indices.progresso]) || 0,
      categoria: linha[indices.categoria] || '',
      avaliador: linha[indices.avaliador] || ''
    };
    
    projetos.push(projeto);
  }
  
  return projetos;
}

/**
 * Converte string de data para objeto Date
 */
function parseData(dataStr) {
  if (!dataStr) return null;
  
  try {
    // Formato esperado: YYYY-MM-DD
    if (typeof dataStr === 'string') {
      const partes = dataStr.split('-');
      if (partes.length === 3) {
        return new Date(parseInt(partes[0]), parseInt(partes[1]) - 1, parseInt(partes[2]));
      }
    }
    
    // Se já é uma data
    if (dataStr instanceof Date) {
      return dataStr;
    }
    
    return new Date(dataStr);
  } catch (error) {
    return null;
  }
}

// ==================== CÁLCULOS ESPECÍFICOS ====================
/**
 * Calcula resumo geral dos projetos
 */
function calcularResumoGeral(projetos) {
  const total = projetos.length;
  const orcamentoTotal = projetos.reduce((sum, p) => sum + p.orcamento, 0);
  const progressoMedio = projetos.reduce((sum, p) => sum + p.progresso, 0) / total;
  
  const statusCount = {};
  projetos.forEach(p => {
    statusCount[p.status] = (statusCount[p.status] || 0) + 1;
  });
  
  return {
    totalProjetos: total,
    orcamentoTotal: orcamentoTotal,
    progressoMedio: Math.round(progressoMedio * 100) / 100,
    statusCount: statusCount
  };
}

/**
 * Calcula distribuição por status
 */
function calcularDistribuicaoStatus(projetos) {
  const distribuicao = {};
  const total = projetos.length;
  
  projetos.forEach(projeto => {
    const status = projeto.status || 'Não Definido';
    distribuicao[status] = (distribuicao[status] || 0) + 1;
  });
  
  // Converter para percentuais
  Object.keys(distribuicao).forEach(status => {
    distribuicao[status] = {
      quantidade: distribuicao[status],
      percentual: Math.round((distribuicao[status] / total) * 100 * 100) / 100
    };
  });
  
  return distribuicao;
}

/**
 * Calcula análise de orçamento
 */
function calcularAnaliseOrcamento(projetos) {
  const orcamentos = projetos.map(p => p.orcamento).filter(o => o > 0);
  
  if (orcamentos.length === 0) {
    return { total: 0, medio: 0, maximo: 0, minimo: 0 };
  }
  
  const total = orcamentos.reduce((sum, o) => sum + o, 0);
  const medio = total / orcamentos.length;
  const maximo = Math.max(...orcamentos);
  const minimo = Math.min(...orcamentos);
  
  return {
    total: total,
    medio: Math.round(medio),
    maximo: maximo,
    minimo: minimo
  };
}

/**
 * Calcula progresso médio por categoria
 */
function calcularProgressoMedio(projetos) {
  const progressoPorCategoria = {};
  
  projetos.forEach(projeto => {
    const categoria = projeto.categoria || 'Não Definido';
    
    if (!progressoPorCategoria[categoria]) {
      progressoPorCategoria[categoria] = {
        total: 0,
        count: 0,
        projetos: []
      };
    }
    
    progressoPorCategoria[categoria].total += projeto.progresso;
    progressoPorCategoria[categoria].count += 1;
    progressoPorCategoria[categoria].projetos.push(projeto.id);
  });
  
  // Calcular médias
  Object.keys(progressoPorCategoria).forEach(categoria => {
    const dados = progressoPorCategoria[categoria];
    dados.media = Math.round((dados.total / dados.count) * 100) / 100;
  });
  
  return progressoPorCategoria;
}

/**
 * Calcula performance dos avaliadores
 */
function calcularPerformanceAvaliadores(projetos) {
  const performance = {};
  
  projetos.forEach(projeto => {
    const avaliador = projeto.avaliador || 'Não Atribuído';
    
    if (!performance[avaliador]) {
      performance[avaliador] = {
        totalProjetos: 0,
        concluidos: 0,
        emAndamento: 0,
        atrasados: 0,
        orcamentoTotal: 0,
        progressoMedio: 0
      };
    }
    
    const perf = performance[avaliador];
    perf.totalProjetos += 1;
    perf.orcamentoTotal += projeto.orcamento;
    perf.progressoMedio += projeto.progresso;
    
    switch (projeto.status) {
      case 'Concluído':
        perf.concluidos += 1;
        break;
      case 'Em Andamento':
        perf.emAndamento += 1;
        break;
      case 'Atrasado':
        perf.atrasados += 1;
        break;
    }
  });
  
  // Calcular médias finais
  Object.keys(performance).forEach(avaliador => {
    const perf = performance[avaliador];
    perf.progressoMedio = Math.round((perf.progressoMedio / perf.totalProjetos) * 100) / 100;
    perf.taxaConclusao = Math.round((perf.concluidos / perf.totalProjetos) * 100 * 100) / 100;
  });
  
  return performance;
}

/**
 * Calcula distribuição por categoria
 */
function calcularDistribuicaoCategoria(projetos) {
  const distribuicao = {};
  
  projetos.forEach(projeto => {
    const categoria = projeto.categoria || 'Não Definido';
    
    if (!distribuicao[categoria]) {
      distribuicao[categoria] = {
        quantidade: 0,
        orcamentoTotal: 0,
        progressoMedio: 0
      };
    }
    
    distribuicao[categoria].quantidade += 1;
    distribuicao[categoria].orcamentoTotal += projeto.orcamento;
    distribuicao[categoria].progressoMedio += projeto.progresso;
  });
  
  // Calcular médias
  Object.keys(distribuicao).forEach(categoria => {
    const dados = distribuicao[categoria];
    dados.progressoMedio = Math.round((dados.progressoMedio / dados.quantidade) * 100) / 100;
  });
  
  return distribuicao;
}

/**
 * Identifica projetos com prazos críticos
 */
function identificarPrazosCriticos(projetos) {
  const hoje = new Date();
  const prazosCriticos = [];
  
  projetos.forEach(projeto => {
    if (projeto.dataFim && projeto.status !== 'Concluído') {
      const diasRestantes = Math.ceil((projeto.dataFim - hoje) / (1000 * 60 * 60 * 24));
      
      if (diasRestantes <= 30) {
        prazosCriticos.push({
          id: projeto.id,
          titulo: projeto.titulo,
          responsavel: projeto.responsavel,
          dataFim: projeto.dataFim,
          diasRestantes: diasRestantes,
          progresso: projeto.progresso,
          status: projeto.status
        });
      }
    }
  });
  
  // Ordenar por dias restantes
  prazosCriticos.sort((a, b) => a.diasRestantes - b.diasRestantes);
  
  return prazosCriticos;
}

/**
 * Calcula tendências temporais
 */
function calcularTendencias(projetos) {
  const anoAtual = new Date().getFullYear();
  const tendencias = {
    projetosPorMes: {},
    orcamentoPorMes: {},
    conclusoesPorMes: {}
  };
  
  projetos.forEach(projeto => {
    if (projeto.dataInicio) {
      const ano = projeto.dataInicio.getFullYear();
      const mes = projeto.dataInicio.getMonth() + 1;
      const chave = `${ano}-${mes.toString().padStart(2, '0')}`;
      
      // Projetos iniciados por mês
      tendencias.projetosPorMes[chave] = (tendencias.projetosPorMes[chave] || 0) + 1;
      
      // Orçamento por mês
      tendencias.orcamentoPorMes[chave] = (tendencias.orcamentoPorMes[chave] || 0) + projeto.orcamento;
    }
    
    // Conclusões por mês
    if (projeto.status === 'Concluído' && projeto.dataFim) {
      const ano = projeto.dataFim.getFullYear();
      const mes = projeto.dataFim.getMonth() + 1;
      const chave = `${ano}-${mes.toString().padStart(2, '0')}`;
      
      tendencias.conclusoesPorMes[chave] = (tendencias.conclusoesPorMes[chave] || 0) + 1;
    }
  });
  
  return tendencias;
}
