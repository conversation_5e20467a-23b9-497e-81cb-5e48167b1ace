/**
 * @version 3.2.4 - CORRIGIDA
 * @description Padronização completa de planilha com conversão de caixa
 */

// Configuração
const CONFIG = {
  ESTILO: {
    FONTE: 'Arial',
    TAMANHO_FONTE: 11,
    CORES: {
      CABECALHO: '#2C3E50',
      FUNDO_ALTERNADO: '#F5F5F5',
      TEXTO: '#333333',
      ERRO: '#FF4444'
    },
    NUMEROS: '#,##0.00;[Red]-#,##0.00',
    LARGURA_COLUNA: { MIN: 120, MAX: 300 },
    ALTURA_LINHA: 21
  },
  CRITERIOS_MINIMOS: {
    LINHAS: 1,
    COLUNAS: 14
  },
  PROCESSAMENTO: {
    TAMANHO_LOTE: 500
  }
};

// Menu customizado
function onOpen() {
  try {
    SpreadsheetApp.getUi()
      .createMenu('Custom Abilities')
      .addItem('Standardize Spreadsheet', 'executarPadronizacaoCompleta')
      .addItem('Convert Case (H, N)', 'convertCaseInColumns')
      .addItem('Fix Row Heights Only', 'padronizarAlturaLinhas')
      .addToUi();
  } catch (error) {
    console.error('Erro no onOpen:', error);
  }
}

// Obter ou criar planilha "Única"
function getOrCreateUnicaSheet() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  let sheet = ss.getSheetByName("Única");
  
  if (!sheet) {
    sheet = ss.getActiveSheet();
    if (sheet && sheet.getName() !== "Única") {
      const allSheetNames = ss.getSheets().map(s => s.getName());
      if (!allSheetNames.includes("Única")) {
        sheet.setName("Única");
        SpreadsheetApp.flush();
      }
    }
  }
  
  if (sheet && !sheet.isSheetHidden()) {
    sheet.activate();
  }
  
  return sheet;
}

// Função principal de padronização
function executarPadronizacaoCompleta() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const sheet = getOrCreateUnicaSheet();
  
  if (!sheet) {
    ss.toast('Erro ao obter planilha', 'Erro', 5);
    return;
  }
  
  try {
    ss.toast('Iniciando padronização...', 'Status', 3);
    
    const ultimaLinha = sheet.getLastRow() || 1;
    const ultimaColuna = sheet.getLastColumn() || CONFIG.CRITERIOS_MINIMOS.COLUNAS;
    
    // Executar todas as formatações
    aplicarEstilosBase(sheet, ultimaLinha, ultimaColuna);
    formatarCabecalho(sheet, ultimaColuna);
    ajustarColunas(sheet, ultimaColuna);
    padronizarAlturaLinhas();
    aplicarFormatacaoNumerica(sheet, ultimaLinha);
    removerEspacosExtras(sheet, ultimaLinha, ultimaColuna);
    convertCaseInColumns();
    aplicarBordas(sheet, ultimaLinha, ultimaColuna);
    
    ss.toast('Padronização concluída!', 'Sucesso', 5);
  } catch (erro) {
    console.error('Erro na padronização:', erro);
    ss.toast('Erro: ' + erro.message, 'Erro', 10);
  }
}

// Aplicar estilos base
function aplicarEstilosBase(sheet, ultimaLinha, ultimaColuna) {
  if (!sheet || ultimaLinha < 1 || ultimaColuna < 1) return;
  
  const range = sheet.getRange(1, 1, ultimaLinha, ultimaColuna);
  range.setFontFamily(CONFIG.ESTILO.FONTE)
       .setFontSize(CONFIG.ESTILO.TAMANHO_FONTE)
       .setVerticalAlignment('middle')
       .setHorizontalAlignment('left')
       .setWrapStrategy(SpreadsheetApp.WrapStrategy.CLIP);
}

// Formatar cabeçalho
function formatarCabecalho(sheet, ultimaColuna) {
  if (!sheet || ultimaColuna < 1) return;
  
  const headerRange = sheet.getRange(1, 1, 1, ultimaColuna);
  headerRange.setBackground(CONFIG.ESTILO.CORES.CABECALHO)
             .setFontColor('#FFFFFF')
             .setFontWeight('bold')
             .setHorizontalAlignment('center');
  
  sheet.setFrozenRows(1);
  
  try {
    if (sheet.getFilter()) sheet.getFilter().remove();
    headerRange.createFilter();
  } catch (e) {
    console.log('Não foi possível criar filtro');
  }
}

// Ajustar largura das colunas
function ajustarColunas(sheet, ultimaColuna) {
  for (let i = 1; i <= ultimaColuna; i++) {
    sheet.autoResizeColumn(i);
    const width = sheet.getColumnWidth(i);
    
    if (width < CONFIG.ESTILO.LARGURA_COLUNA.MIN) {
      sheet.setColumnWidth(i, CONFIG.ESTILO.LARGURA_COLUNA.MIN);
    } else if (width > CONFIG.ESTILO.LARGURA_COLUNA.MAX) {
      sheet.setColumnWidth(i, CONFIG.ESTILO.LARGURA_COLUNA.MAX);
    }
  }
}

// Padronizar altura das linhas
function padronizarAlturaLinhas() {
  const sheet = getOrCreateUnicaSheet();
  if (!sheet) return;
  
  const ultimaLinha = sheet.getLastRow();
  const BATCH_SIZE = 200;
  
  // Cabeçalho
  sheet.setRowHeight(1, CONFIG.ESTILO.ALTURA_LINHA + 4);
  
  // Demais linhas em lotes
  for (let row = 2; row <= ultimaLinha; row += BATCH_SIZE) {
    const numRows = Math.min(BATCH_SIZE, ultimaLinha - row + 1);
    try {
      sheet.setRowHeights(row, numRows, CONFIG.ESTILO.ALTURA_LINHA);
    } catch (e) {
      // Tentar linha por linha se falhar
      for (let i = 0; i < numRows; i++) {
        try {
          sheet.setRowHeight(row + i, CONFIG.ESTILO.ALTURA_LINHA);
        } catch (err) {
          console.log(`Erro na linha ${row + i}`);
        }
      }
    }
  }
}

// Formatação numérica (colunas C-H)
function aplicarFormatacaoNumerica(sheet, ultimaLinha) {
  if (sheet.getMaxColumns() < 8 || ultimaLinha < 2) return;
  
  const range = sheet.getRange(2, 3, ultimaLinha - 1, 6); // C2:H[ultimaLinha]
  range.setNumberFormat(CONFIG.ESTILO.NUMEROS)
       .setHorizontalAlignment('right');
}

// Remover espaços extras
function removerEspacosExtras(sheet, ultimaLinha, ultimaColuna) {
  if (ultimaLinha < 2) return;
  
  const range = sheet.getRange(2, 1, ultimaLinha - 1, ultimaColuna);
  const values = range.getValues();
  
  const newValues = values.map(row => 
    row.map(cell => 
      typeof cell === 'string' ? cell.trim() : cell
    )
  );
  
  range.setValues(newValues);
}

// Aplicar bordas
function aplicarBordas(sheet, ultimaLinha, ultimaColuna) {
  if (ultimaLinha < 2) return;
  
  const range = sheet.getRange(2, 1, ultimaLinha - 1, ultimaColuna);
  range.setBorder(true, true, true, true, true, true, 
                  '#D3D3D3', SpreadsheetApp.BorderStyle.SOLID_THIN);
}

// Converter caixa nas colunas H e N
function convertCaseInColumns() {
  const sheet = getOrCreateUnicaSheet();
  if (!sheet) return;
  
  const lastRow = sheet.getLastRow();
  if (lastRow < 2) return;
  
  const lowercaseWords = [
    'a', 'e', 'i', 'o', 'u', 'as', 'os', 'um', 'uma',
    'de', 'da', 'do', 'das', 'dos', 'em', 'na', 'no',
    'para', 'por', 'com', 'sem', 'que', 'se', 'mas'
  ];
  
  // Processar colunas H e N
  [8, 14].forEach(colIndex => {
    if (colIndex <= sheet.getMaxColumns()) {
      processColumn(sheet, colIndex, lastRow, lowercaseWords);
    }
  });
}

// Processar uma coluna
function processColumn(sheet, colIndex, lastRow, lowercaseWords) {
  const range = sheet.getRange(2, colIndex, lastRow - 1, 1);
  const values = range.getValues();
  
  const newValues = values.map(row => {
    const cell = row[0];
    if (typeof cell !== 'string' || !cell.trim()) return row;
    
    const words = cell.trim().toLowerCase().split(/\s+/);
    const processed = words.map((word, index) => {
      if (index === 0 || !lowercaseWords.includes(word)) {
        return word.charAt(0).toUpperCase() + word.slice(1);
      }
      return word;
    });
    
    return [processed.join(' ')];
  });
  
  range.setValues(newValues);
}
