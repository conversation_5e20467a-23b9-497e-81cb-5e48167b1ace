/**
 * ========================================
 * DASHBOARD DE PROJETOS FACEPE - WEB APP COM GOOGLE SHEETS
 * Sistema com conexão via link do Google Sheets
 * Versão: 1.0.0 | Data: 2025
 * ========================================
 */

// ==================== CONFIGURAÇÕES GLOBAIS ====================
const CONFIG = {
  // Nomes das abas
  ABA_DADOS: 'Dados_Projetos',
  ABA_DASHBOARD: 'Dashboard',
  ABA_METRICAS: 'Métricas',
  ABA_GRAFICOS: 'Gráficos',
  
  // Cores para status
  CORES: {
    'Concluído': '#4CAF50',
    'Em Andamento': '#FF9800', 
    'Atrasado': '#F44336',
    'Cancelado': '#9E9E9E',
    'Pausado': '#9C27B0'
  },
  
  // Configurações de formatação
  FONTE_TITULO: 'Roboto',
  TAMANHO_TITULO: 14,
  TAMANHO_TEXTO: 11
};

// ==================== FUNÇÕES WEB APP ====================
/**
 * Função principal para servir a aplicação web
 */
function doGet(e) {
  try {
    console.log('🌐 Iniciando Web App...');
    
    // Criar template HTML principal
    const template = HtmlService.createTemplateFromFile('index');
    
    // Verificar se existem dados
    const temDados = verificarExistenciaDados();
    template.temDados = temDados;
    
    if (temDados) {
      // Passar dados para o template
      template.dadosDashboard = obterDadosDashboard();
      template.metricas = obterMetricasCompletas();
      template.graficos = obterDadosGraficos();
    }
    
    // Retornar página HTML
    return template.evaluate()
      .setTitle('📊 Dashboard de Projetos FACEPE')
      .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL)
      .addMetaTag('viewport', 'width=device-width, initial-scale=1');
      
  } catch (error) {
    console.error('❌ Erro na Web App:', error);
    return criarPaginaErro(error.message);
  }
}

/**
 * Função para incluir arquivos HTML/CSS/JS
 */
function include(filename) {
  return HtmlService.createHtmlOutputFromFile(filename).getContent();
}

/**
 * Processa link do Google Sheets
 */
function processarLinkGoogleSheets(linkPlanilha) {
  try {
    console.log(`🔗 Processando link: ${linkPlanilha}`);
    
    // Extrair ID da planilha do link
    const planilhaId = extrairIdDaPlanilha(linkPlanilha);
    
    if (!planilhaId) {
      throw new Error('Link inválido. Use um link válido do Google Sheets.');
    }
    
    // Verificar acesso à planilha
    let planilhaOrigem;
    try {
      planilhaOrigem = SpreadsheetApp.openById(planilhaId);
    } catch (error) {
      throw new Error('Não foi possível acessar a planilha. Verifique se o link está correto e se você tem permissão de acesso.');
    }
    
    // Obter primeira aba com dados
    const aba = planilhaOrigem.getSheets()[0];
    const dados = aba.getDataRange().getValues();
    
    if (dados.length === 0) {
      throw new Error('A planilha está vazia');
    }
    
    if (dados.length === 1) {
      throw new Error('A planilha contém apenas cabeçalhos. Adicione dados nas linhas seguintes.');
    }
    
    // Salvar dados na planilha principal
    salvarDadosNaPlanilha(dados);
    
    // Calcular métricas
    calcularMetricas();
    
    console.log('✅ Link processado com sucesso');
    
    return {
      sucesso: true,
      mensagem: `Planilha conectada com sucesso! ${dados.length - 1} projetos importados.`,
      totalLinhas: dados.length - 1,
      nomePlanilha: planilhaOrigem.getName()
    };
    
  } catch (error) {
    console.error('❌ Erro no processamento:', error);
    return {
      sucesso: false,
      mensagem: error.message
    };
  }
}

/**
 * Extrai ID da planilha de diferentes formatos de link
 */
function extrairIdDaPlanilha(link) {
  try {
    // Remover espaços e quebras de linha
    link = link.trim();
    
    // Padrões de URL do Google Sheets
    const padroes = [
      /\/spreadsheets\/d\/([a-zA-Z0-9-_]+)/,  // URL padrão
      /^([a-zA-Z0-9-_]+)$/,                   // Apenas ID
      /id=([a-zA-Z0-9-_]+)/,                  // URL com parâmetro id
      /\/d\/([a-zA-Z0-9-_]+)\/edit/           // URL de edição
    ];
    
    for (const padrao of padroes) {
      const match = link.match(padrao);
      if (match) {
        return match[1];
      }
    }
    
    return null;
  } catch (error) {
    return null;
  }
}

/**
 * Testa conexão com planilha
 */
function testarConexaoPlanilha(linkPlanilha) {
  try {
    const planilhaId = extrairIdDaPlanilha(linkPlanilha);
    
    if (!planilhaId) {
      return {
        sucesso: false,
        mensagem: 'Link inválido'
      };
    }
    
    const planilha = SpreadsheetApp.openById(planilhaId);
    const aba = planilha.getSheets()[0];
    const dados = aba.getDataRange().getValues();
    
    return {
      sucesso: true,
      mensagem: 'Conexão bem-sucedida',
      nomePlanilha: planilha.getName(),
      nomeAba: aba.getName(),
      totalLinhas: dados.length - 1,
      colunas: dados.length > 0 ? dados[0] : []
    };
    
  } catch (error) {
    return {
      sucesso: false,
      mensagem: 'Erro ao acessar planilha: ' + error.message
    };
  }
}

/**
 * Salva dados na planilha principal
 */
function salvarDadosNaPlanilha(dados) {
  const planilha = SpreadsheetApp.getActiveSpreadsheet();
  
  // Criar/obter aba de dados
  let abaDados = planilha.getSheetByName(CONFIG.ABA_DADOS);
  if (!abaDados) {
    abaDados = planilha.insertSheet(CONFIG.ABA_DADOS);
  }
  
  // Limpar dados existentes
  abaDados.clear();
  
  // Inserir novos dados
  if (dados.length > 0) {
    abaDados.getRange(1, 1, dados.length, dados[0].length).setValues(dados);
    
    // Formatar cabeçalho
    formatarCabecalho(abaDados, dados[0].length);
    
    // Aplicar formatação condicional
    aplicarFormatacaoCondicional(abaDados, dados.length);
  }
  
  // Criar estrutura se não existir
  criarEstruturaPlanilha();
}

/**
 * Verifica se existem dados na planilha
 */
function verificarExistenciaDados() {
  try {
    const planilha = SpreadsheetApp.getActiveSpreadsheet();
    const abaDados = planilha.getSheetByName(CONFIG.ABA_DADOS);
    
    return abaDados && abaDados.getLastRow() > 1;
  } catch (error) {
    return false;
  }
}

/**
 * Obtém dados do dashboard para a web app
 */
function obterDadosDashboard() {
  try {
    const planilha = SpreadsheetApp.getActiveSpreadsheet();
    const abaDados = planilha.getSheetByName(CONFIG.ABA_DADOS);
    
    if (!abaDados) return [];
    
    const dados = abaDados.getDataRange().getValues();
    
    if (dados.length <= 1) return [];
    
    // Converter para formato JSON
    const cabecalho = dados[0];
    const projetos = [];
    
    for (let i = 1; i < dados.length; i++) {
      const projeto = {};
      cabecalho.forEach((col, index) => {
        projeto[col] = dados[i][index];
      });
      projetos.push(projeto);
    }
    
    return projetos;
    
  } catch (error) {
    console.error('Erro ao obter dados:', error);
    return [];
  }
}

/**
 * Obtém métricas completas para a web app
 */
function obterMetricasCompletas() {
  try {
    const planilha = SpreadsheetApp.getActiveSpreadsheet();
    const abaDados = planilha.getSheetByName(CONFIG.ABA_DADOS);
    
    if (!abaDados) return {};
    
    const dados = obterDadosProjetos(abaDados);
    
    if (dados.length === 0) return {};
    
    return {
      resumoGeral: calcularResumoGeral(dados),
      statusDistribuicao: calcularDistribuicaoStatus(dados),
      orcamentoAnalise: calcularAnaliseOrcamento(dados),
      avaliadoresPerformance: calcularPerformanceAvaliadores(dados),
      categoriaDistribuicao: calcularDistribuicaoCategoria(dados),
      prazosCriticos: identificarPrazosCriticos(dados),
      tendencias: calcularTendencias(dados)
    };
    
  } catch (error) {
    console.error('Erro ao calcular métricas:', error);
    return {};
  }
}

/**
 * Obtém dados para gráficos
 */
function obterDadosGraficos() {
  try {
    const planilha = SpreadsheetApp.getActiveSpreadsheet();
    const abaDados = planilha.getSheetByName(CONFIG.ABA_DADOS);
    
    if (!abaDados) return {};
    
    const dados = abaDados.getDataRange().getValues();
    
    if (dados.length <= 1) return {};
    
    // Dados para gráfico de status
    const statusCount = {};
    for (let i = 1; i < dados.length; i++) {
      const status = dados[i][3] || 'Não Definido';
      statusCount[status] = (statusCount[status] || 0) + 1;
    }
    
    // Dados para gráfico de categorias
    const categoriaCount = {};
    for (let i = 1; i < dados.length; i++) {
      const categoria = dados[i][8] || 'Não Definido';
      categoriaCount[categoria] = (categoriaCount[categoria] || 0) + 1;
    }
    
    // Dados para gráfico de orçamento
    const orcamentoCategoria = {};
    for (let i = 1; i < dados.length; i++) {
      const categoria = dados[i][8] || 'Não Definido';
      const orcamento = parseFloat(dados[i][6]) || 0;
      orcamentoCategoria[categoria] = (orcamentoCategoria[categoria] || 0) + orcamento;
    }
    
    return {
      status: statusCount,
      categorias: categoriaCount,
      orcamento: orcamentoCategoria,
      cores: CONFIG.CORES
    };
    
  } catch (error) {
    console.error('Erro ao obter dados de gráficos:', error);
    return {};
  }
}

/**
 * Cria página de erro
 */
function criarPaginaErro(mensagem) {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Erro - Dashboard FACEPE</title>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1">
      <style>
        body { 
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
          margin: 0; padding: 20px; 
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          min-height: 100vh;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .error-container { 
          background: white;
          padding: 40px;
          border-radius: 15px;
          box-shadow: 0 10px 30px rgba(0,0,0,0.2);
          text-align: center;
          max-width: 500px;
        }
        .error-icon { font-size: 64px; margin-bottom: 20px; }
        h2 { color: #d32f2f; margin-bottom: 20px; }
        .error-message { 
          background: #ffebee; 
          padding: 20px; 
          border-radius: 8px; 
          margin: 20px 0;
          border-left: 4px solid #d32f2f;
        }
        .btn { 
          background: #1976d2; 
          color: white; 
          padding: 12px 24px; 
          border: none; 
          border-radius: 6px; 
          cursor: pointer;
          font-size: 16px;
          margin: 10px;
        }
        .btn:hover { background: #1565c0; }
      </style>
    </head>
    <body>
      <div class="error-container">
        <div class="error-icon">❌</div>
        <h2>Erro na Aplicação</h2>
        <div class="error-message">
          <strong>Erro:</strong> ${mensagem}
        </div>
        <p>Verifique a configuração e tente novamente.</p>
        <button class="btn" onclick="location.reload()">🔄 Tentar Novamente</button>
      </div>
    </body>
    </html>
  `;
  
  return HtmlService.createHtmlOutput(html);
}

// ==================== FUNÇÕES DE PROCESSAMENTO ====================
/**
 * Obtém dados estruturados da aba de projetos
 */
function obterDadosProjetos(aba) {
  const dados = aba.getDataRange().getValues();

  if (dados.length <= 1) return [];

  const cabecalho = dados[0];
  const projetos = [];

  // Mapear índices das colunas (busca flexível)
  const indices = {
    id: cabecalho.findIndex(col => col.toString().toLowerCase().includes('id')),
    titulo: cabecalho.findIndex(col => col.toString().toLowerCase().includes('título') || col.toString().toLowerCase().includes('titulo')),
    responsavel: cabecalho.findIndex(col => col.toString().toLowerCase().includes('responsável') || col.toString().toLowerCase().includes('responsavel')),
    status: cabecalho.findIndex(col => col.toString().toLowerCase().includes('status')),
    dataInicio: cabecalho.findIndex(col => col.toString().toLowerCase().includes('início') || col.toString().toLowerCase().includes('inicio')),
    dataFim: cabecalho.findIndex(col => col.toString().toLowerCase().includes('fim')),
    orcamento: cabecalho.findIndex(col => col.toString().toLowerCase().includes('orçamento') || col.toString().toLowerCase().includes('orcamento')),
    progresso: cabecalho.findIndex(col => col.toString().toLowerCase().includes('progresso')),
    categoria: cabecalho.findIndex(col => col.toString().toLowerCase().includes('categoria')),
    avaliador: cabecalho.findIndex(col => col.toString().toLowerCase().includes('avaliador'))
  };

  // Processar cada linha de dados
  for (let i = 1; i < dados.length; i++) {
    const linha = dados[i];

    const projeto = {
      id: indices.id >= 0 ? linha[indices.id] || '' : '',
      titulo: indices.titulo >= 0 ? linha[indices.titulo] || '' : '',
      responsavel: indices.responsavel >= 0 ? linha[indices.responsavel] || '' : '',
      status: indices.status >= 0 ? linha[indices.status] || '' : '',
      dataInicio: indices.dataInicio >= 0 ? parseData(linha[indices.dataInicio]) : null,
      dataFim: indices.dataFim >= 0 ? parseData(linha[indices.dataFim]) : null,
      orcamento: indices.orcamento >= 0 ? parseFloat(linha[indices.orcamento]) || 0 : 0,
      progresso: indices.progresso >= 0 ? parseFloat(linha[indices.progresso]) || 0 : 0,
      categoria: indices.categoria >= 0 ? linha[indices.categoria] || '' : '',
      avaliador: indices.avaliador >= 0 ? linha[indices.avaliador] || '' : ''
    };

    projetos.push(projeto);
  }

  return projetos;
}

/**
 * Converte string de data para objeto Date
 */
function parseData(dataStr) {
  if (!dataStr) return null;

  try {
    // Se já é uma data
    if (dataStr instanceof Date) {
      return dataStr;
    }

    // Formato esperado: YYYY-MM-DD ou DD/MM/YYYY
    if (typeof dataStr === 'string') {
      // Formato YYYY-MM-DD
      if (dataStr.includes('-')) {
        const partes = dataStr.split('-');
        if (partes.length === 3) {
          return new Date(parseInt(partes[0]), parseInt(partes[1]) - 1, parseInt(partes[2]));
        }
      }

      // Formato DD/MM/YYYY
      if (dataStr.includes('/')) {
        const partes = dataStr.split('/');
        if (partes.length === 3) {
          return new Date(parseInt(partes[2]), parseInt(partes[1]) - 1, parseInt(partes[0]));
        }
      }
    }

    return new Date(dataStr);
  } catch (error) {
    return null;
  }
}

/**
 * Calcula resumo geral dos projetos
 */
function calcularResumoGeral(projetos) {
  const total = projetos.length;
  const orcamentoTotal = projetos.reduce((sum, p) => sum + p.orcamento, 0);
  const progressoMedio = projetos.reduce((sum, p) => sum + p.progresso, 0) / total;

  const statusCount = {};
  projetos.forEach(p => {
    statusCount[p.status] = (statusCount[p.status] || 0) + 1;
  });

  return {
    totalProjetos: total,
    orcamentoTotal: orcamentoTotal,
    progressoMedio: Math.round(progressoMedio * 100) / 100,
    statusCount: statusCount
  };
}

/**
 * Calcula distribuição por status
 */
function calcularDistribuicaoStatus(projetos) {
  const distribuicao = {};
  const total = projetos.length;

  projetos.forEach(projeto => {
    const status = projeto.status || 'Não Definido';
    distribuicao[status] = (distribuicao[status] || 0) + 1;
  });

  // Converter para percentuais
  Object.keys(distribuicao).forEach(status => {
    distribuicao[status] = {
      quantidade: distribuicao[status],
      percentual: Math.round((distribuicao[status] / total) * 100 * 100) / 100
    };
  });

  return distribuicao;
}

/**
 * Calcula análise de orçamento
 */
function calcularAnaliseOrcamento(projetos) {
  const orcamentos = projetos.map(p => p.orcamento).filter(o => o > 0);

  if (orcamentos.length === 0) {
    return { total: 0, medio: 0, maximo: 0, minimo: 0 };
  }

  const total = orcamentos.reduce((sum, o) => sum + o, 0);
  const medio = total / orcamentos.length;
  const maximo = Math.max(...orcamentos);
  const minimo = Math.min(...orcamentos);

  return {
    total: total,
    medio: Math.round(medio),
    maximo: maximo,
    minimo: minimo
  };
}

/**
 * Calcula performance dos avaliadores
 */
function calcularPerformanceAvaliadores(projetos) {
  const performance = {};

  projetos.forEach(projeto => {
    const avaliador = projeto.avaliador || 'Não Atribuído';

    if (!performance[avaliador]) {
      performance[avaliador] = {
        totalProjetos: 0,
        concluidos: 0,
        emAndamento: 0,
        atrasados: 0,
        orcamentoTotal: 0,
        progressoMedio: 0
      };
    }

    const perf = performance[avaliador];
    perf.totalProjetos += 1;
    perf.orcamentoTotal += projeto.orcamento;
    perf.progressoMedio += projeto.progresso;

    switch (projeto.status) {
      case 'Concluído':
        perf.concluidos += 1;
        break;
      case 'Em Andamento':
        perf.emAndamento += 1;
        break;
      case 'Atrasado':
        perf.atrasados += 1;
        break;
    }
  });

  // Calcular médias finais
  Object.keys(performance).forEach(avaliador => {
    const perf = performance[avaliador];
    perf.progressoMedio = Math.round((perf.progressoMedio / perf.totalProjetos) * 100) / 100;
    perf.taxaConclusao = Math.round((perf.concluidos / perf.totalProjetos) * 100 * 100) / 100;
  });

  return performance;
}

/**
 * Calcula distribuição por categoria
 */
function calcularDistribuicaoCategoria(projetos) {
  const distribuicao = {};

  projetos.forEach(projeto => {
    const categoria = projeto.categoria || 'Não Definido';

    if (!distribuicao[categoria]) {
      distribuicao[categoria] = {
        quantidade: 0,
        orcamentoTotal: 0,
        progressoMedio: 0
      };
    }

    distribuicao[categoria].quantidade += 1;
    distribuicao[categoria].orcamentoTotal += projeto.orcamento;
    distribuicao[categoria].progressoMedio += projeto.progresso;
  });

  // Calcular médias
  Object.keys(distribuicao).forEach(categoria => {
    const dados = distribuicao[categoria];
    dados.progressoMedio = Math.round((dados.progressoMedio / dados.quantidade) * 100) / 100;
  });

  return distribuicao;
}

/**
 * Identifica projetos com prazos críticos
 */
function identificarPrazosCriticos(projetos) {
  const hoje = new Date();
  const prazosCriticos = [];

  projetos.forEach(projeto => {
    if (projeto.dataFim && projeto.status !== 'Concluído') {
      const diasRestantes = Math.ceil((projeto.dataFim - hoje) / (1000 * 60 * 60 * 24));

      if (diasRestantes <= 30) {
        prazosCriticos.push({
          id: projeto.id,
          titulo: projeto.titulo,
          responsavel: projeto.responsavel,
          dataFim: projeto.dataFim,
          diasRestantes: diasRestantes,
          progresso: projeto.progresso,
          status: projeto.status
        });
      }
    }
  });

  // Ordenar por dias restantes
  prazosCriticos.sort((a, b) => a.diasRestantes - b.diasRestantes);

  return prazosCriticos;
}

/**
 * Calcula tendências temporais
 */
function calcularTendencias(projetos) {
  const tendencias = {
    projetosPorMes: {},
    orcamentoPorMes: {},
    conclusoesPorMes: {}
  };

  projetos.forEach(projeto => {
    if (projeto.dataInicio) {
      const ano = projeto.dataInicio.getFullYear();
      const mes = projeto.dataInicio.getMonth() + 1;
      const chave = `${ano}-${mes.toString().padStart(2, '0')}`;

      // Projetos iniciados por mês
      tendencias.projetosPorMes[chave] = (tendencias.projetosPorMes[chave] || 0) + 1;

      // Orçamento por mês
      tendencias.orcamentoPorMes[chave] = (tendencias.orcamentoPorMes[chave] || 0) + projeto.orcamento;
    }

    // Conclusões por mês
    if (projeto.status === 'Concluído' && projeto.dataFim) {
      const ano = projeto.dataFim.getFullYear();
      const mes = projeto.dataFim.getMonth() + 1;
      const chave = `${ano}-${mes.toString().padStart(2, '0')}`;

      tendencias.conclusoesPorMes[chave] = (tendencias.conclusoesPorMes[chave] || 0) + 1;
    }
  });

  return tendencias;
}

// ==================== FUNÇÕES DE FORMATAÇÃO ====================
/**
 * Formata o cabeçalho da tabela de dados
 */
function formatarCabecalho(aba, numColunas) {
  const cabecalho = aba.getRange(1, 1, 1, numColunas);

  cabecalho
    .setBackground('#1f4e79')
    .setFontColor('#ffffff')
    .setFontWeight('bold')
    .setFontSize(CONFIG.TAMANHO_TITULO)
    .setHorizontalAlignment('center')
    .setVerticalAlignment('middle');

  // Ajustar largura das colunas
  aba.autoResizeColumns(1, numColunas);

  // Congelar primeira linha
  aba.setFrozenRows(1);
}

/**
 * Aplica formatação condicional baseada no status
 */
function aplicarFormatacaoCondicional(aba, numLinhas) {
  if (numLinhas <= 1) return;

  // Encontrar coluna de status
  const dados = aba.getDataRange().getValues();
  const cabecalho = dados[0];
  const colunaStatus = cabecalho.findIndex(col => col.toString().toLowerCase().includes('status'));

  if (colunaStatus < 0) return;

  const rangeStatus = aba.getRange(2, colunaStatus + 1, numLinhas - 1, 1);

  // Limpar regras existentes
  aba.clearConditionalFormatRules();

  const regras = [];

  // Criar regras para cada status
  Object.keys(CONFIG.CORES).forEach(status => {
    const regra = SpreadsheetApp.newConditionalFormatRule()
      .whenTextEqualTo(status)
      .setBackground(CONFIG.CORES[status])
      .setFontColor('#ffffff')
      .setRanges([rangeStatus])
      .build();
    regras.push(regra);
  });

  aba.setConditionalFormatRules(regras);
}

/**
 * Calcula métricas e atualiza planilha
 */
function calcularMetricas() {
  try {
    console.log('📊 Calculando métricas...');

    const planilha = SpreadsheetApp.getActiveSpreadsheet();
    const abaDados = planilha.getSheetByName(CONFIG.ABA_DADOS);

    if (!abaDados) {
      throw new Error('Aba de dados não encontrada.');
    }

    // Obter dados
    const dados = obterDadosProjetos(abaDados);

    if (dados.length === 0) {
      throw new Error('Nenhum dado encontrado para calcular métricas.');
    }

    console.log('✅ Métricas calculadas com sucesso');

  } catch (error) {
    console.error('❌ Erro no cálculo de métricas:', error);
    throw error;
  }
}

/**
 * Cria estrutura básica da planilha
 */
function criarEstruturaPlanilha() {
  const planilha = SpreadsheetApp.getActiveSpreadsheet();

  // Criar abas necessárias se não existirem
  const abasNecessarias = [
    CONFIG.ABA_DADOS,
    CONFIG.ABA_DASHBOARD,
    CONFIG.ABA_METRICAS,
    CONFIG.ABA_GRAFICOS
  ];

  abasNecessarias.forEach(nomeAba => {
    let aba = planilha.getSheetByName(nomeAba);
    if (!aba) {
      aba = planilha.insertSheet(nomeAba);
      console.log(`✅ Aba "${nomeAba}" criada`);
    }
  });

  // Remover aba padrão se existir e estiver vazia
  try {
    const abaPadrao = planilha.getSheetByName('Planilha1');
    if (abaPadrao && abaPadrao.getLastRow() <= 1) {
      planilha.deleteSheet(abaPadrao);
    }
  } catch (error) {
    // Ignorar erro se aba não existir
  }
}
