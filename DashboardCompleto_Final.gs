/**
 * ========================================
 * DASHBOARD DE PROJETOS FACEPE - CÓDIGO COMPLETO FINAL
 * Sistema com conexão via link do Google Sheets
 * Versão: 1.0.0 | Data: 2025
 * ========================================
 */

// ==================== CONFIGURAÇÕES GLOBAIS ====================
const CONFIG = {
  ABA_DADOS: 'Dados_Projetos',
  ABA_DASHBOARD: 'Dashboard',
  ABA_METRICAS: 'Métricas',
  ABA_GRAFICOS: 'Gráficos',
  
  CORES: {
    'Concluído': '#4CAF50',
    'Em Andamento': '#FF9800', 
    'Atrasado': '#F44336',
    'Cancelado': '#9E9E9E',
    'Pausado': '#9C27B0'
  },
  
  FONTE_TITULO: 'Roboto',
  TAMANHO_TITULO: 14,
  TAMANHO_TEXTO: 11
};

// ==================== FUNÇÕES WEB APP ====================
/**
 * Função principal para servir a aplicação web
 */
function doGet(e) {
  try {
    console.log('🌐 Iniciando Web App...');
    
    const template = HtmlService.createTemplateFromFile('index');
    
    const temDados = verificarExistenciaDados();
    template.temDados = temDados;
    
    if (temDados) {
      template.dadosDashboard = obterDadosDashboard();
      template.metricas = obterMetricasCompletas();
      template.graficos = obterDadosGraficos();
    }
    
    return template.evaluate()
      .setTitle('📊 Dashboard de Projetos FACEPE')
      .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL)
      .addMetaTag('viewport', 'width=device-width, initial-scale=1');
      
  } catch (error) {
    console.error('❌ Erro na Web App:', error);
    return criarPaginaErro(error.message);
  }
}

/**
 * Função para incluir arquivos HTML/CSS/JS
 */
function include(filename) {
  return HtmlService.createHtmlOutputFromFile(filename).getContent();
}

/**
 * Processa link do Google Sheets e retorna dados completos
 */
function processarLinkGoogleSheets(linkPlanilha) {
  try {
    console.log(`🔗 Processando link: ${linkPlanilha}`);

    const planilhaId = extrairIdDaPlanilha(linkPlanilha);

    if (!planilhaId) {
      throw new Error('Link inválido. Use um link válido do Google Sheets.');
    }

    let planilhaOrigem;
    try {
      planilhaOrigem = SpreadsheetApp.openById(planilhaId);
    } catch (error) {
      throw new Error('Não foi possível acessar a planilha. Verifique se o link está correto e se você tem permissão de acesso.');
    }

    const aba = planilhaOrigem.getSheets()[0];
    const dados = aba.getDataRange().getValues();

    if (dados.length === 0) {
      throw new Error('A planilha está vazia');
    }

    if (dados.length === 1) {
      throw new Error('A planilha contém apenas cabeçalhos. Adicione dados nas linhas seguintes.');
    }

    // Salvar dados na planilha principal
    salvarDadosNaPlanilha(dados);

    // Calcular métricas diretamente
    const dadosProjetos = obterDadosProjetosFromArray(dados);
    const metricas = calcularTodasMetricas(dadosProjetos);
    const graficos = calcularDadosGraficos(dados);

    console.log('✅ Link processado com sucesso');

    return {
      sucesso: true,
      mensagem: `Planilha conectada com sucesso! ${dados.length - 1} projetos importados.`,
      totalLinhas: dados.length - 1,
      nomePlanilha: planilhaOrigem.getName(),
      // Retornar dados completos para evitar reload
      dadosDashboard: obterDadosDashboardFromArray(dados),
      metricas: metricas,
      graficos: graficos
    };

  } catch (error) {
    console.error('❌ Erro no processamento:', error);
    return {
      sucesso: false,
      mensagem: error.message
    };
  }
}

/**
 * Obtém dados de projetos diretamente de um array
 */
function obterDadosProjetosFromArray(dados) {
  if (dados.length <= 1) return [];

  const cabecalho = dados[0];
  const projetos = [];

  const indices = {
    id: cabecalho.findIndex(col => col.toString().toLowerCase().includes('id')),
    titulo: cabecalho.findIndex(col => col.toString().toLowerCase().includes('título') || col.toString().toLowerCase().includes('titulo')),
    responsavel: cabecalho.findIndex(col => col.toString().toLowerCase().includes('responsável') || col.toString().toLowerCase().includes('responsavel')),
    status: cabecalho.findIndex(col => col.toString().toLowerCase().includes('status')),
    dataInicio: cabecalho.findIndex(col => col.toString().toLowerCase().includes('início') || col.toString().toLowerCase().includes('inicio')),
    dataFim: cabecalho.findIndex(col => col.toString().toLowerCase().includes('fim')),
    orcamento: cabecalho.findIndex(col => col.toString().toLowerCase().includes('orçamento') || col.toString().toLowerCase().includes('orcamento')),
    progresso: cabecalho.findIndex(col => col.toString().toLowerCase().includes('progresso')),
    categoria: cabecalho.findIndex(col => col.toString().toLowerCase().includes('categoria')),
    avaliador: cabecalho.findIndex(col => col.toString().toLowerCase().includes('avaliador'))
  };

  for (let i = 1; i < dados.length; i++) {
    const linha = dados[i];

    const projeto = {
      id: indices.id >= 0 ? linha[indices.id] || '' : '',
      titulo: indices.titulo >= 0 ? linha[indices.titulo] || '' : '',
      responsavel: indices.responsavel >= 0 ? linha[indices.responsavel] || '' : '',
      status: indices.status >= 0 ? linha[indices.status] || '' : '',
      dataInicio: indices.dataInicio >= 0 ? parseData(linha[indices.dataInicio]) : null,
      dataFim: indices.dataFim >= 0 ? parseData(linha[indices.dataFim]) : null,
      orcamento: indices.orcamento >= 0 ? parseFloat(linha[indices.orcamento]) || 0 : 0,
      progresso: indices.progresso >= 0 ? parseFloat(linha[indices.progresso]) || 0 : 0,
      categoria: indices.categoria >= 0 ? linha[indices.categoria] || '' : '',
      avaliador: indices.avaliador >= 0 ? linha[indices.avaliador] || '' : ''
    };

    projetos.push(projeto);
  }

  return projetos;
}

/**
 * Obtém dados do dashboard diretamente de um array
 */
function obterDadosDashboardFromArray(dados) {
  if (dados.length <= 1) return [];

  const cabecalho = dados[0];
  const projetos = [];

  for (let i = 1; i < dados.length; i++) {
    const projeto = {};
    cabecalho.forEach((col, index) => {
      projeto[col] = dados[i][index];
    });
    projetos.push(projeto);
  }

  return projetos;
}

/**
 * Calcula todas as métricas de uma vez
 */
function calcularTodasMetricas(projetos) {
  if (projetos.length === 0) return {};

  return {
    resumoGeral: calcularResumoGeral(projetos),
    statusDistribuicao: calcularDistribuicaoStatus(projetos),
    orcamentoAnalise: calcularAnaliseOrcamento(projetos),
    avaliadoresPerformance: calcularPerformanceAvaliadores(projetos),
    categoriaDistribuicao: calcularDistribuicaoCategoria(projetos),
    prazosCriticos: identificarPrazosCriticos(projetos),
    tendencias: calcularTendencias(projetos)
  };
}

/**
 * Calcula dados para gráficos diretamente de um array
 */
function calcularDadosGraficos(dados) {
  if (dados.length <= 1) return {};

  // Dados para gráfico de status
  const statusCount = {};
  for (let i = 1; i < dados.length; i++) {
    const status = dados[i][3] || 'Não Definido';
    statusCount[status] = (statusCount[status] || 0) + 1;
  }

  // Dados para gráfico de categorias
  const categoriaCount = {};
  for (let i = 1; i < dados.length; i++) {
    const categoria = dados[i][8] || 'Não Definido';
    categoriaCount[categoria] = (categoriaCount[categoria] || 0) + 1;
  }

  // Dados para gráfico de orçamento
  const orcamentoCategoria = {};
  for (let i = 1; i < dados.length; i++) {
    const categoria = dados[i][8] || 'Não Definido';
    const orcamento = parseFloat(dados[i][6]) || 0;
    orcamentoCategoria[categoria] = (orcamentoCategoria[categoria] || 0) + orcamento;
  }

  return {
    status: statusCount,
    categorias: categoriaCount,
    orcamento: orcamentoCategoria,
    cores: CONFIG.CORES
  };
}

/**
 * Extrai ID da planilha de diferentes formatos de link
 */
function extrairIdDaPlanilha(link) {
  try {
    link = link.trim();
    
    const padroes = [
      /\/spreadsheets\/d\/([a-zA-Z0-9-_]+)/,
      /^([a-zA-Z0-9-_]+)$/,
      /id=([a-zA-Z0-9-_]+)/,
      /\/d\/([a-zA-Z0-9-_]+)\/edit/
    ];
    
    for (const padrao of padroes) {
      const match = link.match(padrao);
      if (match) {
        return match[1];
      }
    }
    
    return null;
  } catch (error) {
    return null;
  }
}

/**
 * Testa conexão com planilha
 */
function testarConexaoPlanilha(linkPlanilha) {
  try {
    const planilhaId = extrairIdDaPlanilha(linkPlanilha);
    
    if (!planilhaId) {
      return {
        sucesso: false,
        mensagem: 'Link inválido'
      };
    }
    
    const planilha = SpreadsheetApp.openById(planilhaId);
    const aba = planilha.getSheets()[0];
    const dados = aba.getDataRange().getValues();
    
    return {
      sucesso: true,
      mensagem: 'Conexão bem-sucedida',
      nomePlanilha: planilha.getName(),
      nomeAba: aba.getName(),
      totalLinhas: dados.length - 1,
      colunas: dados.length > 0 ? dados[0] : []
    };
    
  } catch (error) {
    return {
      sucesso: false,
      mensagem: 'Erro ao acessar planilha: ' + error.message
    };
  }
}

/**
 * Salva dados na planilha principal
 */
function salvarDadosNaPlanilha(dados) {
  const planilha = SpreadsheetApp.getActiveSpreadsheet();
  
  let abaDados = planilha.getSheetByName(CONFIG.ABA_DADOS);
  if (!abaDados) {
    abaDados = planilha.insertSheet(CONFIG.ABA_DADOS);
  }
  
  abaDados.clear();
  
  if (dados.length > 0) {
    abaDados.getRange(1, 1, dados.length, dados[0].length).setValues(dados);
    formatarCabecalho(abaDados, dados[0].length);
    aplicarFormatacaoCondicional(abaDados, dados.length);
  }
  
  criarEstruturaPlanilha();
}

/**
 * Verifica se existem dados na planilha
 */
function verificarExistenciaDados() {
  try {
    const planilha = SpreadsheetApp.getActiveSpreadsheet();
    const abaDados = planilha.getSheetByName(CONFIG.ABA_DADOS);
    
    return abaDados && abaDados.getLastRow() > 1;
  } catch (error) {
    return false;
  }
}

/**
 * Obtém dados do dashboard para a web app
 */
function obterDadosDashboard() {
  try {
    const planilha = SpreadsheetApp.getActiveSpreadsheet();
    const abaDados = planilha.getSheetByName(CONFIG.ABA_DADOS);
    
    if (!abaDados) return [];
    
    const dados = abaDados.getDataRange().getValues();
    
    if (dados.length <= 1) return [];
    
    const cabecalho = dados[0];
    const projetos = [];
    
    for (let i = 1; i < dados.length; i++) {
      const projeto = {};
      cabecalho.forEach((col, index) => {
        projeto[col] = dados[i][index];
      });
      projetos.push(projeto);
    }
    
    return projetos;
    
  } catch (error) {
    console.error('Erro ao obter dados:', error);
    return [];
  }
}

/**
 * Obtém métricas completas para a web app
 */
function obterMetricasCompletas() {
  try {
    const planilha = SpreadsheetApp.getActiveSpreadsheet();
    const abaDados = planilha.getSheetByName(CONFIG.ABA_DADOS);
    
    if (!abaDados) return {};
    
    const dados = obterDadosProjetos(abaDados);
    
    if (dados.length === 0) return {};
    
    return {
      resumoGeral: calcularResumoGeral(dados),
      statusDistribuicao: calcularDistribuicaoStatus(dados),
      orcamentoAnalise: calcularAnaliseOrcamento(dados),
      avaliadoresPerformance: calcularPerformanceAvaliadores(dados),
      categoriaDistribuicao: calcularDistribuicaoCategoria(dados),
      prazosCriticos: identificarPrazosCriticos(dados),
      tendencias: calcularTendencias(dados)
    };
    
  } catch (error) {
    console.error('Erro ao calcular métricas:', error);
    return {};
  }
}

/**
 * Obtém dados para gráficos
 */
function obterDadosGraficos() {
  try {
    const planilha = SpreadsheetApp.getActiveSpreadsheet();
    const abaDados = planilha.getSheetByName(CONFIG.ABA_DADOS);
    
    if (!abaDados) return {};
    
    const dados = abaDados.getDataRange().getValues();
    
    if (dados.length <= 1) return {};
    
    const statusCount = {};
    for (let i = 1; i < dados.length; i++) {
      const status = dados[i][3] || 'Não Definido';
      statusCount[status] = (statusCount[status] || 0) + 1;
    }
    
    const categoriaCount = {};
    for (let i = 1; i < dados.length; i++) {
      const categoria = dados[i][8] || 'Não Definido';
      categoriaCount[categoria] = (categoriaCount[categoria] || 0) + 1;
    }
    
    const orcamentoCategoria = {};
    for (let i = 1; i < dados.length; i++) {
      const categoria = dados[i][8] || 'Não Definido';
      const orcamento = parseFloat(dados[i][6]) || 0;
      orcamentoCategoria[categoria] = (orcamentoCategoria[categoria] || 0) + orcamento;
    }
    
    return {
      status: statusCount,
      categorias: categoriaCount,
      orcamento: orcamentoCategoria,
      cores: CONFIG.CORES
    };
    
  } catch (error) {
    console.error('Erro ao obter dados de gráficos:', error);
    return {};
  }
}

/**
 * Cria página de erro
 */
function criarPaginaErro(mensagem) {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Erro - Dashboard FACEPE</title>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1">
      <style>
        body { 
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
          margin: 0; padding: 20px; 
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          min-height: 100vh;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .error-container { 
          background: white;
          padding: 40px;
          border-radius: 15px;
          box-shadow: 0 10px 30px rgba(0,0,0,0.2);
          text-align: center;
          max-width: 500px;
        }
        .error-icon { font-size: 64px; margin-bottom: 20px; }
        h2 { color: #d32f2f; margin-bottom: 20px; }
        .error-message { 
          background: #ffebee; 
          padding: 20px; 
          border-radius: 8px; 
          margin: 20px 0;
          border-left: 4px solid #d32f2f;
        }
        .btn { 
          background: #1976d2; 
          color: white; 
          padding: 12px 24px; 
          border: none; 
          border-radius: 6px; 
          cursor: pointer;
          font-size: 16px;
          margin: 10px;
        }
        .btn:hover { background: #1565c0; }
      </style>
    </head>
    <body>
      <div class="error-container">
        <div class="error-icon">❌</div>
        <h2>Erro na Aplicação</h2>
        <div class="error-message">
          <strong>Erro:</strong> ${mensagem}
        </div>
        <p>Verifique a configuração e tente novamente.</p>
        <button class="btn" onclick="location.reload()">🔄 Tentar Novamente</button>
      </div>
    </body>
    </html>
  `;
  
  return HtmlService.createHtmlOutput(html);
}

// ==================== FUNÇÕES DE TESTE ====================
/**
 * Teste básico de comunicação
 */
function testeConexao() {
  try {
    const agora = new Date().toLocaleString('pt-BR');
    console.log('✅ Teste de conexão executado em:', agora);
    return `Comunicação OK! Timestamp: ${agora}`;
  } catch (error) {
    console.error('❌ Erro no teste de conexão:', error);
    throw new Error('Erro no teste: ' + error.message);
  }
}

/**
 * Verifica status dos dados
 */
function verificarStatusDados() {
  try {
    console.log('🔍 Verificando status dos dados...');

    const planilha = SpreadsheetApp.getActiveSpreadsheet();
    const abaDados = planilha.getSheetByName(CONFIG.ABA_DADOS);

    const resultado = {
      temDados: false,
      totalProjetos: 0,
      nomesPlanilha: planilha.getName(),
      abas: planilha.getSheets().map(aba => aba.getName()),
      timestamp: new Date().toLocaleString('pt-BR')
    };

    if (abaDados) {
      const ultimaLinha = abaDados.getLastRow();
      resultado.temDados = ultimaLinha > 1;
      resultado.totalProjetos = ultimaLinha > 1 ? ultimaLinha - 1 : 0;
      resultado.ultimaLinha = ultimaLinha;

      if (ultimaLinha > 0) {
        const dados = abaDados.getDataRange().getValues();
        resultado.cabecalhos = dados[0];
        resultado.totalColunas = dados[0].length;
      }
    }

    console.log('✅ Status verificado:', resultado);
    return resultado;

  } catch (error) {
    console.error('❌ Erro ao verificar dados:', error);
    return {
      erro: true,
      mensagem: error.message,
      timestamp: new Date().toLocaleString('pt-BR')
    };
  }
}

/**
 * Teste simples de acesso a planilha
 */
function testeAcessoPlanilha() {
  try {
    console.log('🔍 Testando acesso à planilha...');

    const planilha = SpreadsheetApp.getActiveSpreadsheet();
    const info = {
      id: planilha.getId(),
      nome: planilha.getName(),
      url: planilha.getUrl(),
      totalAbas: planilha.getSheets().length,
      abas: planilha.getSheets().map(aba => ({
        nome: aba.getName(),
        linhas: aba.getLastRow(),
        colunas: aba.getLastColumn()
      }))
    };

    console.log('✅ Acesso à planilha OK:', info);
    return info;

  } catch (error) {
    console.error('❌ Erro no acesso à planilha:', error);
    throw new Error('Erro no acesso: ' + error.message);
  }
}

// ==================== FUNÇÕES DE MAPEAMENTO ====================
/**
 * Obtém dados da planilha para mapeamento de colunas
 */
function obterDadosParaMapeamento(linkPlanilha) {
  try {
    console.log(`🔗 Obtendo TODOS os dados para mapeamento: ${linkPlanilha}`);

    const planilhaId = extrairIdDaPlanilha(linkPlanilha);

    if (!planilhaId) {
      throw new Error('Link inválido. Use um link válido do Google Sheets.');
    }

    let planilhaOrigem;
    try {
      planilhaOrigem = SpreadsheetApp.openById(planilhaId);
    } catch (error) {
      throw new Error('Não foi possível acessar a planilha. Verifique se o link está correto e se você tem permissão de acesso.');
    }

    const aba = planilhaOrigem.getSheets()[0];

    // OBTER TODAS AS LINHAS - método mais robusto
    const ultimaLinha = aba.getLastRow();
    const ultimaColuna = aba.getLastColumn();

    console.log(`📊 Planilha detectada: ${ultimaLinha} linhas × ${ultimaColuna} colunas`);

    if (ultimaLinha === 0 || ultimaColuna === 0) {
      throw new Error('A planilha está vazia');
    }

    if (ultimaLinha === 1) {
      throw new Error('A planilha contém apenas cabeçalhos. Adicione dados nas linhas seguintes.');
    }

    // GARANTIR QUE PEGA TODAS AS LINHAS
    const dados = [];

    // Cabeçalhos (linha 1)
    const cabecalhos = aba.getRange(1, 1, 1, ultimaColuna).getValues()[0];
    dados.push(cabecalhos);

    // TODAS as linhas de dados (linha 2 até a última)
    if (ultimaLinha > 1) {
      const linhasDados = aba.getRange(2, 1, ultimaLinha - 1, ultimaColuna).getValues();
      dados.push(...linhasDados);
    }

    console.log(`✅ SUCESSO: ${dados.length - 1} linhas de dados carregadas`);
    console.log(`📋 Colunas encontradas: ${cabecalhos.join(', ')}`);

    return {
      sucesso: true,
      mensagem: `${dados.length - 1} registros carregados com sucesso`,
      nomePlanilha: planilhaOrigem.getName(),
      nomeAba: aba.getName(),
      totalLinhas: dados.length - 1,
      totalColunas: ultimaColuna,
      colunas: cabecalhos,
      dadosBrutos: dados // TODOS os dados garantidos
    };

  } catch (error) {
    console.error('❌ Erro ao obter dados para mapeamento:', error);
    return {
      sucesso: false,
      mensagem: error.message
    };
  }
}

/**
 * Processa dados com mapeamento personalizado
 */
function processarComMapeamentoPersonalizado(dadosBrutos, mapeamento) {
  try {
    console.log('🎯 Processando com mapeamento personalizado');
    console.log('Mapeamento recebido:', mapeamento);

    if (!dadosBrutos || dadosBrutos.length <= 1) {
      throw new Error('Dados insuficientes para processamento');
    }

    // Validar mapeamento obrigatório (apenas ID e Título)
    if (mapeamento.id === undefined || mapeamento.titulo === undefined) {
      throw new Error('Mapeamento obrigatório incompleto (ID e Título são obrigatórios)');
    }

    // Processar dados com mapeamento
    const dadosProcessados = processarDadosComMapeamento(dadosBrutos, mapeamento);

    // Salvar dados na planilha principal
    salvarDadosProcessados(dadosProcessados);

    // Calcular métricas
    const metricas = calcularTodasMetricas(dadosProcessados);
    const graficos = calcularDadosGraficosProcessados(dadosProcessados);

    console.log('✅ Processamento com mapeamento concluído');

    return {
      sucesso: true,
      mensagem: `Dashboard gerado com sucesso! ${dadosProcessados.length} projetos processados.`,
      dadosDashboard: obterDadosDashboardProcessados(dadosProcessados),
      metricas: metricas,
      graficos: graficos
    };

  } catch (error) {
    console.error('❌ Erro no processamento com mapeamento:', error);
    return {
      sucesso: false,
      mensagem: error.message
    };
  }
}

/**
 * Processa dados aplicando o mapeamento de colunas
 */
function processarDadosComMapeamento(dadosBrutos, mapeamento) {
  const cabecalho = dadosBrutos[0];
  const projetos = [];

  console.log('Processando', dadosBrutos.length - 1, 'linhas de dados');

  for (let i = 1; i < dadosBrutos.length; i++) {
    const linha = dadosBrutos[i];

    const projeto = {
      id: mapeamento.id >= 0 ? linha[mapeamento.id] || `Projeto_${i}` : `Projeto_${i}`,
      titulo: mapeamento.titulo >= 0 ? linha[mapeamento.titulo] || 'Sem Título' : 'Sem Título',
      responsavel: mapeamento.responsavel >= 0 ? linha[mapeamento.responsavel] || 'Não Informado' : 'Não Informado',
      status: mapeamento.status >= 0 ? linha[mapeamento.status] || 'Não Definido' : 'Não Definido',
      dataInicio: mapeamento.dataInicio >= 0 ? parseData(linha[mapeamento.dataInicio]) : null,
      dataFim: mapeamento.dataFim >= 0 ? parseData(linha[mapeamento.dataFim]) : null,
      orcamento: mapeamento.orcamento >= 0 ? parseFloat(linha[mapeamento.orcamento]) || 0 : 0,
      progresso: mapeamento.progresso >= 0 ? parseFloat(linha[mapeamento.progresso]) || 0 : 0,
      categoria: mapeamento.categoria >= 0 ? linha[mapeamento.categoria] || 'Geral' : 'Geral',
      avaliador: mapeamento.avaliador >= 0 ? linha[mapeamento.avaliador] || 'Não Atribuído' : 'Não Atribuído'
    };

    projetos.push(projeto);
  }

  console.log('Projetos processados:', projetos.length);
  return projetos;
}

/**
 * Salva dados processados na planilha principal
 */
function salvarDadosProcessados(dadosProcessados) {
  const planilha = SpreadsheetApp.getActiveSpreadsheet();

  let abaDados = planilha.getSheetByName(CONFIG.ABA_DADOS);
  if (!abaDados) {
    abaDados = planilha.insertSheet(CONFIG.ABA_DADOS);
  }

  abaDados.clear();

  // Criar cabeçalho padronizado
  const cabecalho = ['ID', 'Título', 'Responsável', 'Status', 'Data_Início', 'Data_Fim', 'Orçamento', 'Progresso', 'Categoria', 'Avaliador'];

  // Converter dados para array
  const dadosParaSalvar = [cabecalho];

  dadosProcessados.forEach(projeto => {
    dadosParaSalvar.push([
      projeto.id,
      projeto.titulo,
      projeto.responsavel,
      projeto.status,
      projeto.dataInicio,
      projeto.dataFim,
      projeto.orcamento,
      projeto.progresso,
      projeto.categoria,
      projeto.avaliador
    ]);
  });

  // Salvar dados
  if (dadosParaSalvar.length > 0) {
    abaDados.getRange(1, 1, dadosParaSalvar.length, dadosParaSalvar[0].length).setValues(dadosParaSalvar);
    formatarCabecalho(abaDados, dadosParaSalvar[0].length);
    aplicarFormatacaoCondicional(abaDados, dadosParaSalvar.length);
  }

  criarEstruturaPlanilha();
}

/**
 * Obtém dados do dashboard de projetos processados
 */
function obterDadosDashboardProcessados(dadosProcessados) {
  return dadosProcessados.map(projeto => ({
    ID: projeto.id,
    Título: projeto.titulo,
    Responsável: projeto.responsavel,
    Status: projeto.status,
    Data_Início: projeto.dataInicio,
    Data_Fim: projeto.dataFim,
    Orçamento: projeto.orcamento,
    Progresso: projeto.progresso,
    Categoria: projeto.categoria,
    Avaliador: projeto.avaliador
  }));
}

/**
 * Calcula dados para gráficos de projetos processados
 */
function calcularDadosGraficosProcessados(dadosProcessados) {
  // Dados para gráfico de status
  const statusCount = {};
  dadosProcessados.forEach(projeto => {
    const status = projeto.status || 'Não Definido';
    statusCount[status] = (statusCount[status] || 0) + 1;
  });

  // Dados para gráfico de categorias
  const categoriaCount = {};
  dadosProcessados.forEach(projeto => {
    const categoria = projeto.categoria || 'Não Definido';
    categoriaCount[categoria] = (categoriaCount[categoria] || 0) + 1;
  });

  // Dados para gráfico de orçamento
  const orcamentoCategoria = {};
  dadosProcessados.forEach(projeto => {
    const categoria = projeto.categoria || 'Não Definido';
    orcamentoCategoria[categoria] = (orcamentoCategoria[categoria] || 0) + projeto.orcamento;
  });

  return {
    status: statusCount,
    categorias: categoriaCount,
    orcamento: orcamentoCategoria,
    cores: CONFIG.CORES
  };
}

// ==================== FUNÇÕES DE PROCESSAMENTO ADICIONAL ====================

/**
 * Processa dados com mapeamento personalizado
 */
function processarComMapeamentoPersonalizado(dadosBrutos, mapeamento) {
  try {
    console.log('🎯 Processando com mapeamento personalizado');
    console.log('Mapeamento recebido:', mapeamento);

    if (!dadosBrutos || dadosBrutos.length <= 1) {
      throw new Error('Dados insuficientes para processamento');
    }

    // Validar mapeamento obrigatório
    if (mapeamento.id === undefined || mapeamento.titulo === undefined || mapeamento.status === undefined) {
      throw new Error('Mapeamento obrigatório incompleto (ID, Título e Status são obrigatórios)');
    }

    // Processar dados com mapeamento
    const dadosProcessados = processarDadosComMapeamento(dadosBrutos, mapeamento);

    // Salvar dados na planilha principal
    salvarDadosProcessados(dadosProcessados);

    // Calcular métricas
    const metricas = calcularTodasMetricas(dadosProcessados);
    const graficos = calcularDadosGraficosProcessados(dadosProcessados);

    console.log('✅ Processamento com mapeamento concluído');

    return {
      sucesso: true,
      mensagem: `Dashboard gerado com sucesso! ${dadosProcessados.length} projetos processados.`,
      dadosDashboard: obterDadosDashboardProcessados(dadosProcessados),
      metricas: metricas,
      graficos: graficos
    };

  } catch (error) {
    console.error('❌ Erro no processamento com mapeamento:', error);
    return {
      sucesso: false,
      mensagem: error.message
    };
  }
}

/**
 * Processa dados aplicando o mapeamento de colunas
 */
function processarDadosComMapeamento(dadosBrutos, mapeamento) {
  const cabecalho = dadosBrutos[0];
  const projetos = [];

  console.log('Processando', dadosBrutos.length - 1, 'linhas de dados');

  for (let i = 1; i < dadosBrutos.length; i++) {
    const linha = dadosBrutos[i];

    const projeto = {
      id: mapeamento.id >= 0 ? linha[mapeamento.id] || '' : '',
      titulo: mapeamento.titulo >= 0 ? linha[mapeamento.titulo] || '' : '',
      responsavel: mapeamento.responsavel >= 0 ? linha[mapeamento.responsavel] || '' : '',
      status: mapeamento.status >= 0 ? linha[mapeamento.status] || '' : '',
      dataInicio: mapeamento.dataInicio >= 0 ? parseData(linha[mapeamento.dataInicio]) : null,
      dataFim: mapeamento.dataFim >= 0 ? parseData(linha[mapeamento.dataFim]) : null,
      orcamento: mapeamento.orcamento >= 0 ? parseFloat(linha[mapeamento.orcamento]) || 0 : 0,
      progresso: mapeamento.progresso >= 0 ? parseFloat(linha[mapeamento.progresso]) || 0 : 0,
      categoria: mapeamento.categoria >= 0 ? linha[mapeamento.categoria] || '' : '',
      avaliador: mapeamento.avaliador >= 0 ? linha[mapeamento.avaliador] || '' : ''
    };

    projetos.push(projeto);
  }

  console.log('Projetos processados:', projetos.length);
  return projetos;
}

/**
 * Salva dados processados na planilha principal
 */
function salvarDadosProcessados(dadosProcessados) {
  const planilha = SpreadsheetApp.getActiveSpreadsheet();

  let abaDados = planilha.getSheetByName(CONFIG.ABA_DADOS);
  if (!abaDados) {
    abaDados = planilha.insertSheet(CONFIG.ABA_DADOS);
  }

  abaDados.clear();

  // Criar cabeçalho padronizado
  const cabecalho = ['ID', 'Título', 'Responsável', 'Status', 'Data_Início', 'Data_Fim', 'Orçamento', 'Progresso', 'Categoria', 'Avaliador'];

  // Converter dados para array
  const dadosParaSalvar = [cabecalho];

  dadosProcessados.forEach(projeto => {
    dadosParaSalvar.push([
      projeto.id,
      projeto.titulo,
      projeto.responsavel,
      projeto.status,
      projeto.dataInicio,
      projeto.dataFim,
      projeto.orcamento,
      projeto.progresso,
      projeto.categoria,
      projeto.avaliador
    ]);
  });

  // Salvar dados
  if (dadosParaSalvar.length > 0) {
    abaDados.getRange(1, 1, dadosParaSalvar.length, dadosParaSalvar[0].length).setValues(dadosParaSalvar);
    formatarCabecalho(abaDados, dadosParaSalvar[0].length);
    aplicarFormatacaoCondicional(abaDados, dadosParaSalvar.length);
  }

  criarEstruturaPlanilha();
}

/**
 * Obtém dados do dashboard de projetos processados
 */
function obterDadosDashboardProcessados(dadosProcessados) {
  return dadosProcessados.map(projeto => ({
    ID: projeto.id,
    Título: projeto.titulo,
    Responsável: projeto.responsavel,
    Status: projeto.status,
    Data_Início: projeto.dataInicio,
    Data_Fim: projeto.dataFim,
    Orçamento: projeto.orcamento,
    Progresso: projeto.progresso,
    Categoria: projeto.categoria,
    Avaliador: projeto.avaliador
  }));
}

/**
 * Calcula dados para gráficos de projetos processados
 */
function calcularDadosGraficosProcessados(dadosProcessados) {
  // Dados para gráfico de status
  const statusCount = {};
  dadosProcessados.forEach(projeto => {
    const status = projeto.status || 'Não Definido';
    statusCount[status] = (statusCount[status] || 0) + 1;
  });

  // Dados para gráfico de categorias
  const categoriaCount = {};
  dadosProcessados.forEach(projeto => {
    const categoria = projeto.categoria || 'Não Definido';
    categoriaCount[categoria] = (categoriaCount[categoria] || 0) + 1;
  });

  // Dados para gráfico de orçamento
  const orcamentoCategoria = {};
  dadosProcessados.forEach(projeto => {
    const categoria = projeto.categoria || 'Não Definido';
    orcamentoCategoria[categoria] = (orcamentoCategoria[categoria] || 0) + projeto.orcamento;
  });

  return {
    status: statusCount,
    categorias: categoriaCount,
    orcamento: orcamentoCategoria,
    cores: CONFIG.CORES
  };
}

// ==================== FUNÇÕES DE PROCESSAMENTO ====================
/**
 * Obtém dados estruturados da aba de projetos
 */
function obterDadosProjetos(aba) {
  const dados = aba.getDataRange().getValues();

  if (dados.length <= 1) return [];

  const cabecalho = dados[0];
  const projetos = [];

  const indices = {
    id: cabecalho.findIndex(col => col.toString().toLowerCase().includes('id')),
    titulo: cabecalho.findIndex(col => col.toString().toLowerCase().includes('título') || col.toString().toLowerCase().includes('titulo')),
    responsavel: cabecalho.findIndex(col => col.toString().toLowerCase().includes('responsável') || col.toString().toLowerCase().includes('responsavel')),
    status: cabecalho.findIndex(col => col.toString().toLowerCase().includes('status')),
    dataInicio: cabecalho.findIndex(col => col.toString().toLowerCase().includes('início') || col.toString().toLowerCase().includes('inicio')),
    dataFim: cabecalho.findIndex(col => col.toString().toLowerCase().includes('fim')),
    orcamento: cabecalho.findIndex(col => col.toString().toLowerCase().includes('orçamento') || col.toString().toLowerCase().includes('orcamento')),
    progresso: cabecalho.findIndex(col => col.toString().toLowerCase().includes('progresso')),
    categoria: cabecalho.findIndex(col => col.toString().toLowerCase().includes('categoria')),
    avaliador: cabecalho.findIndex(col => col.toString().toLowerCase().includes('avaliador'))
  };

  for (let i = 1; i < dados.length; i++) {
    const linha = dados[i];

    const projeto = {
      id: indices.id >= 0 ? linha[indices.id] || '' : '',
      titulo: indices.titulo >= 0 ? linha[indices.titulo] || '' : '',
      responsavel: indices.responsavel >= 0 ? linha[indices.responsavel] || '' : '',
      status: indices.status >= 0 ? linha[indices.status] || '' : '',
      dataInicio: indices.dataInicio >= 0 ? parseData(linha[indices.dataInicio]) : null,
      dataFim: indices.dataFim >= 0 ? parseData(linha[indices.dataFim]) : null,
      orcamento: indices.orcamento >= 0 ? parseFloat(linha[indices.orcamento]) || 0 : 0,
      progresso: indices.progresso >= 0 ? parseFloat(linha[indices.progresso]) || 0 : 0,
      categoria: indices.categoria >= 0 ? linha[indices.categoria] || '' : '',
      avaliador: indices.avaliador >= 0 ? linha[indices.avaliador] || '' : ''
    };

    projetos.push(projeto);
  }

  return projetos;
}

/**
 * Converte string de data para objeto Date
 */
function parseData(dataStr) {
  if (!dataStr) return null;

  try {
    if (dataStr instanceof Date) {
      return dataStr;
    }

    if (typeof dataStr === 'string') {
      if (dataStr.includes('-')) {
        const partes = dataStr.split('-');
        if (partes.length === 3) {
          return new Date(parseInt(partes[0]), parseInt(partes[1]) - 1, parseInt(partes[2]));
        }
      }

      if (dataStr.includes('/')) {
        const partes = dataStr.split('/');
        if (partes.length === 3) {
          return new Date(parseInt(partes[2]), parseInt(partes[1]) - 1, parseInt(partes[0]));
        }
      }
    }

    return new Date(dataStr);
  } catch (error) {
    return null;
  }
}

/**
 * Calcula resumo geral dos projetos
 */
function calcularResumoGeral(projetos) {
  const total = projetos.length;
  const orcamentoTotal = projetos.reduce((sum, p) => sum + p.orcamento, 0);
  const progressoMedio = projetos.reduce((sum, p) => sum + p.progresso, 0) / total;

  const statusCount = {};
  projetos.forEach(p => {
    statusCount[p.status] = (statusCount[p.status] || 0) + 1;
  });

  return {
    totalProjetos: total,
    orcamentoTotal: orcamentoTotal,
    progressoMedio: Math.round(progressoMedio * 100) / 100,
    statusCount: statusCount
  };
}

/**
 * Calcula distribuição por status
 */
function calcularDistribuicaoStatus(projetos) {
  const distribuicao = {};
  const total = projetos.length;

  projetos.forEach(projeto => {
    const status = projeto.status || 'Não Definido';
    distribuicao[status] = (distribuicao[status] || 0) + 1;
  });

  Object.keys(distribuicao).forEach(status => {
    distribuicao[status] = {
      quantidade: distribuicao[status],
      percentual: Math.round((distribuicao[status] / total) * 100 * 100) / 100
    };
  });

  return distribuicao;
}

/**
 * Calcula análise de orçamento
 */
function calcularAnaliseOrcamento(projetos) {
  const orcamentos = projetos.map(p => p.orcamento).filter(o => o > 0);

  if (orcamentos.length === 0) {
    return { total: 0, medio: 0, maximo: 0, minimo: 0 };
  }

  const total = orcamentos.reduce((sum, o) => sum + o, 0);
  const medio = total / orcamentos.length;
  const maximo = Math.max(...orcamentos);
  const minimo = Math.min(...orcamentos);

  return {
    total: total,
    medio: Math.round(medio),
    maximo: maximo,
    minimo: minimo
  };
}

/**
 * Calcula performance dos avaliadores
 */
function calcularPerformanceAvaliadores(projetos) {
  const performance = {};

  projetos.forEach(projeto => {
    const avaliador = projeto.avaliador || 'Não Atribuído';

    if (!performance[avaliador]) {
      performance[avaliador] = {
        totalProjetos: 0,
        concluidos: 0,
        emAndamento: 0,
        atrasados: 0,
        orcamentoTotal: 0,
        progressoMedio: 0
      };
    }

    const perf = performance[avaliador];
    perf.totalProjetos += 1;
    perf.orcamentoTotal += projeto.orcamento;
    perf.progressoMedio += projeto.progresso;

    switch (projeto.status) {
      case 'Concluído':
        perf.concluidos += 1;
        break;
      case 'Em Andamento':
        perf.emAndamento += 1;
        break;
      case 'Atrasado':
        perf.atrasados += 1;
        break;
    }
  });

  Object.keys(performance).forEach(avaliador => {
    const perf = performance[avaliador];
    perf.progressoMedio = Math.round((perf.progressoMedio / perf.totalProjetos) * 100) / 100;
    perf.taxaConclusao = Math.round((perf.concluidos / perf.totalProjetos) * 100 * 100) / 100;
  });

  return performance;
}

/**
 * Calcula distribuição por categoria
 */
function calcularDistribuicaoCategoria(projetos) {
  const distribuicao = {};

  projetos.forEach(projeto => {
    const categoria = projeto.categoria || 'Não Definido';

    if (!distribuicao[categoria]) {
      distribuicao[categoria] = {
        quantidade: 0,
        orcamentoTotal: 0,
        progressoMedio: 0
      };
    }

    distribuicao[categoria].quantidade += 1;
    distribuicao[categoria].orcamentoTotal += projeto.orcamento;
    distribuicao[categoria].progressoMedio += projeto.progresso;
  });

  Object.keys(distribuicao).forEach(categoria => {
    const dados = distribuicao[categoria];
    dados.progressoMedio = Math.round((dados.progressoMedio / dados.quantidade) * 100) / 100;
  });

  return distribuicao;
}

/**
 * Identifica projetos com prazos críticos
 */
function identificarPrazosCriticos(projetos) {
  const hoje = new Date();
  const prazosCriticos = [];

  projetos.forEach(projeto => {
    if (projeto.dataFim && projeto.status !== 'Concluído') {
      const diasRestantes = Math.ceil((projeto.dataFim - hoje) / (1000 * 60 * 60 * 24));

      if (diasRestantes <= 30) {
        prazosCriticos.push({
          id: projeto.id,
          titulo: projeto.titulo,
          responsavel: projeto.responsavel,
          dataFim: projeto.dataFim,
          diasRestantes: diasRestantes,
          progresso: projeto.progresso,
          status: projeto.status
        });
      }
    }
  });

  prazosCriticos.sort((a, b) => a.diasRestantes - b.diasRestantes);

  return prazosCriticos;
}

/**
 * Calcula tendências temporais
 */
function calcularTendencias(projetos) {
  const tendencias = {
    projetosPorMes: {},
    orcamentoPorMes: {},
    conclusoesPorMes: {}
  };

  projetos.forEach(projeto => {
    if (projeto.dataInicio) {
      const ano = projeto.dataInicio.getFullYear();
      const mes = projeto.dataInicio.getMonth() + 1;
      const chave = `${ano}-${mes.toString().padStart(2, '0')}`;

      tendencias.projetosPorMes[chave] = (tendencias.projetosPorMes[chave] || 0) + 1;
      tendencias.orcamentoPorMes[chave] = (tendencias.orcamentoPorMes[chave] || 0) + projeto.orcamento;
    }

    if (projeto.status === 'Concluído' && projeto.dataFim) {
      const ano = projeto.dataFim.getFullYear();
      const mes = projeto.dataFim.getMonth() + 1;
      const chave = `${ano}-${mes.toString().padStart(2, '0')}`;

      tendencias.conclusoesPorMes[chave] = (tendencias.conclusoesPorMes[chave] || 0) + 1;
    }
  });

  return tendencias;
}

// ==================== FUNÇÕES DE FORMATAÇÃO ====================
/**
 * Formata o cabeçalho da tabela de dados
 */
function formatarCabecalho(aba, numColunas) {
  const cabecalho = aba.getRange(1, 1, 1, numColunas);

  cabecalho
    .setBackground('#1f4e79')
    .setFontColor('#ffffff')
    .setFontWeight('bold')
    .setFontSize(CONFIG.TAMANHO_TITULO)
    .setHorizontalAlignment('center')
    .setVerticalAlignment('middle');

  aba.autoResizeColumns(1, numColunas);
  aba.setFrozenRows(1);
}

/**
 * Aplica formatação condicional baseada no status
 */
function aplicarFormatacaoCondicional(aba, numLinhas) {
  if (numLinhas <= 1) return;

  const dados = aba.getDataRange().getValues();
  const cabecalho = dados[0];
  const colunaStatus = cabecalho.findIndex(col => col.toString().toLowerCase().includes('status'));

  if (colunaStatus < 0) return;

  const rangeStatus = aba.getRange(2, colunaStatus + 1, numLinhas - 1, 1);

  aba.clearConditionalFormatRules();

  const regras = [];

  Object.keys(CONFIG.CORES).forEach(status => {
    const regra = SpreadsheetApp.newConditionalFormatRule()
      .whenTextEqualTo(status)
      .setBackground(CONFIG.CORES[status])
      .setFontColor('#ffffff')
      .setRanges([rangeStatus])
      .build();
    regras.push(regra);
  });

  aba.setConditionalFormatRules(regras);
}

/**
 * Calcula métricas e atualiza planilha
 */
function calcularMetricas() {
  try {
    console.log('📊 Calculando métricas...');

    const planilha = SpreadsheetApp.getActiveSpreadsheet();
    const abaDados = planilha.getSheetByName(CONFIG.ABA_DADOS);

    if (!abaDados) {
      throw new Error('Aba de dados não encontrada.');
    }

    const dados = obterDadosProjetos(abaDados);

    if (dados.length === 0) {
      throw new Error('Nenhum dado encontrado para calcular métricas.');
    }

    console.log('✅ Métricas calculadas com sucesso');

  } catch (error) {
    console.error('❌ Erro no cálculo de métricas:', error);
    throw error;
  }
}

/**
 * Cria estrutura básica da planilha
 */
function criarEstruturaPlanilha() {
  const planilha = SpreadsheetApp.getActiveSpreadsheet();

  const abasNecessarias = [
    CONFIG.ABA_DADOS,
    CONFIG.ABA_DASHBOARD,
    CONFIG.ABA_METRICAS,
    CONFIG.ABA_GRAFICOS
  ];

  abasNecessarias.forEach(nomeAba => {
    let aba = planilha.getSheetByName(nomeAba);
    if (!aba) {
      aba = planilha.insertSheet(nomeAba);
      console.log(`✅ Aba "${nomeAba}" criada`);
    }
  });

  try {
    const abaPadrao = planilha.getSheetByName('Planilha1');
    if (abaPadrao && abaPadrao.getLastRow() <= 1) {
      planilha.deleteSheet(abaPadrao);
    }
  } catch (error) {
    // Ignorar erro se aba não existir
  }
}
