<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Dashboard de Projetos FACEPE - Debug</title>
    
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #1976d2;
            color: white;
            border-radius: 10px;
        }
        
        .debug-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #1976d2;
        }
        
        .link-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .input-group {
            margin: 15px 0;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .input-group input {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        
        .btn {
            background: #1976d2;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        
        .btn:hover {
            background: #1565c0;
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .dashboard {
            display: none;
            margin-top: 30px;
        }
        
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .metric-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 2px solid #e9ecef;
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #1976d2;
        }
        
        .metric-label {
            color: #666;
            margin-top: 5px;
        }
        
        .status-list {
            margin: 20px 0;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            margin: 5px 0;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #1976d2;
        }
        
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border: 1px solid #e9ecef;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Dashboard de Projetos FACEPE</h1>
            <p>Versão Debug - Diagnóstico de Problemas</p>
        </div>
        
        <div class="debug-info">
            <h3>🔍 Informações de Debug</h3>
            <div id="debugInfo">Carregando informações...</div>
        </div>
        
        <div id="linkSection" class="link-section">
            <h3>🔗 Conectar Planilha Google Sheets</h3>
            
            <div class="input-group">
                <label for="linkInput">Link da Planilha:</label>
                <input type="url" id="linkInput" placeholder="https://docs.google.com/spreadsheets/d/..." />
            </div>
            
            <button class="btn" onclick="testarConexao()" id="btnTestar">
                🔍 Testar Conexão
            </button>
            <button class="btn" onclick="conectarPlanilha()" id="btnConectar" disabled>
                🔗 Conectar Planilha
            </button>
            
            <div id="alertContainer"></div>
            
            <div id="infoConexao" style="display: none;">
                <h4>📋 Informações da Planilha</h4>
                <div id="detalhesConexao"></div>
            </div>
        </div>
        
        <div id="dashboardSection" class="dashboard">
            <h3>📊 Dashboard</h3>
            
            <div class="metrics">
                <div class="metric-card">
                    <div class="metric-value" id="totalProjetos">0</div>
                    <div class="metric-label">Total de Projetos</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="orcamentoTotal">R$ 0</div>
                    <div class="metric-label">Orçamento Total</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="progressoMedio">0%</div>
                    <div class="metric-label">Progresso Médio</div>
                </div>
            </div>
            
            <div class="status-list">
                <h4>🎯 Status dos Projetos</h4>
                <div id="statusContainer"></div>
            </div>
            
            <button class="btn" onclick="novaConexao()">
                🔄 Conectar Nova Planilha
            </button>
        </div>
        
        <div class="debug-info">
            <h3>📋 Dados Carregados</h3>
            <h4>Métricas:</h4>
            <pre id="debugMetricas">Nenhuma métrica carregada</pre>
            <h4>Gráficos:</h4>
            <pre id="debugGraficos">Nenhum dado de gráfico carregado</pre>
        </div>
    </div>

    <script>
        // Variáveis globais
        let dadosDashboard = [];
        let metricas = {};
        let graficos = {};
        let conexaoTestada = false;
        
        // Debug inicial
        function atualizarDebugInfo() {
            const debugDiv = document.getElementById('debugInfo');
            const agora = new Date().toLocaleString('pt-BR');
            
            debugDiv.innerHTML = `
                <strong>Timestamp:</strong> ${agora}<br>
                <strong>User Agent:</strong> ${navigator.userAgent}<br>
                <strong>URL:</strong> ${window.location.href}<br>
                <strong>Dados carregados:</strong> ${dadosDashboard.length} projetos<br>
                <strong>Métricas:</strong> ${Object.keys(metricas).length} tipos<br>
                <strong>Gráficos:</strong> ${Object.keys(graficos).length} tipos
            `;
            
            // Atualizar dados de debug
            document.getElementById('debugMetricas').textContent = JSON.stringify(metricas, null, 2);
            document.getElementById('debugGraficos').textContent = JSON.stringify(graficos, null, 2);
        }
        
        // Verificar se há dados ao carregar
        <?
        if (typeof temDados !== 'undefined' && temDados) {
        ?>
            dadosDashboard = <?= JSON.stringify(dadosDashboard || []) ?>;
            metricas = <?= JSON.stringify(metricas || {}) ?>;
            graficos = <?= JSON.stringify(graficos || {}) ?>;
            
            document.addEventListener('DOMContentLoaded', function() {
                console.log('DOM carregado com dados');
                document.getElementById('linkSection').style.display = 'none';
                document.getElementById('dashboardSection').style.display = 'block';
                
                atualizarDebugInfo();
                carregarDashboard();
            });
        <?
        } else {
        ?>
            document.addEventListener('DOMContentLoaded', function() {
                console.log('DOM carregado sem dados');
                document.getElementById('linkSection').style.display = 'block';
                document.getElementById('dashboardSection').style.display = 'none';
                
                atualizarDebugInfo();
            });
        <?
        }
        ?>
        
        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Configurando event listeners');
            
            const linkInput = document.getElementById('linkInput');
            if (linkInput) {
                linkInput.addEventListener('input', function() {
                    const link = linkInput.value.trim();
                    const isValid = validarLinkGoogleSheets(link);
                    
                    linkInput.style.borderColor = link === '' ? '#ddd' : (isValid ? '#4caf50' : '#f44336');
                    
                    if (!isValid) {
                        conexaoTestada = false;
                        document.getElementById('btnConectar').disabled = true;
                    }
                });
            }
        });
        
        // Validar formato do link Google Sheets
        function validarLinkGoogleSheets(link) {
            if (!link) return false;
            
            const padroes = [
                /https:\/\/docs\.google\.com\/spreadsheets\/d\/[a-zA-Z0-9-_]+/,
                /^[a-zA-Z0-9-_]+$/
            ];
            
            return padroes.some(padrao => padrao.test(link));
        }
        
        // Testar conexão
        function testarConexao() {
            const link = document.getElementById('linkInput').value.trim();
            
            if (!link) {
                mostrarAlerta('Por favor, insira o link da planilha', 'error');
                return;
            }
            
            if (!validarLinkGoogleSheets(link)) {
                mostrarAlerta('Link inválido. Use um link válido do Google Sheets', 'error');
                return;
            }
            
            const btnTestar = document.getElementById('btnTestar');
            btnTestar.disabled = true;
            btnTestar.textContent = 'Testando...';
            
            google.script.run
                .withSuccessHandler(function(resultado) {
                    btnTestar.disabled = false;
                    btnTestar.textContent = '🔍 Testar Conexão';
                    
                    if (resultado.sucesso) {
                        mostrarAlerta('Conexão bem-sucedida!', 'success');
                        mostrarInfoConexao(resultado);
                        conexaoTestada = true;
                        document.getElementById('btnConectar').disabled = false;
                    } else {
                        mostrarAlerta(resultado.mensagem, 'error');
                        conexaoTestada = false;
                        document.getElementById('btnConectar').disabled = true;
                    }
                })
                .withFailureHandler(function(erro) {
                    btnTestar.disabled = false;
                    btnTestar.textContent = '🔍 Testar Conexão';
                    mostrarAlerta('Erro ao testar conexão: ' + erro.message, 'error');
                    conexaoTestada = false;
                    document.getElementById('btnConectar').disabled = true;
                })
                .testarConexaoPlanilha(link);
        }
        
        // Mostrar informações da conexão
        function mostrarInfoConexao(info) {
            const container = document.getElementById('detalhesConexao');
            const infoDiv = document.getElementById('infoConexao');
            
            container.innerHTML = `
                <strong>Nome:</strong> ${info.nomePlanilha}<br>
                <strong>Aba:</strong> ${info.nomeAba}<br>
                <strong>Linhas:</strong> ${info.totalLinhas} projetos<br>
                <strong>Colunas:</strong> ${info.colunas.length} colunas
            `;
            
            infoDiv.style.display = 'block';
        }
        
        // Conectar planilha
        function conectarPlanilha() {
            if (!conexaoTestada) {
                mostrarAlerta('Teste a conexão primeiro', 'error');
                return;
            }
            
            const link = document.getElementById('linkInput').value.trim();
            
            const btnConectar = document.getElementById('btnConectar');
            btnConectar.disabled = true;
            btnConectar.textContent = 'Conectando...';
            
            google.script.run
                .withSuccessHandler(function(resultado) {
                    btnConectar.disabled = false;
                    btnConectar.textContent = '🔗 Conectar Planilha';
                    
                    if (resultado.sucesso) {
                        mostrarAlerta(resultado.mensagem, 'success');
                        setTimeout(() => {
                            location.reload();
                        }, 2000);
                    } else {
                        mostrarAlerta(resultado.mensagem, 'error');
                    }
                })
                .withFailureHandler(function(erro) {
                    btnConectar.disabled = false;
                    btnConectar.textContent = '🔗 Conectar Planilha';
                    mostrarAlerta('Erro ao conectar planilha: ' + erro.message, 'error');
                })
                .processarLinkGoogleSheets(link);
        }
        
        // Nova conexão
        function novaConexao() {
            if (confirm('Deseja conectar uma nova planilha?')) {
                location.reload();
            }
        }
        
        // Mostrar alerta
        function mostrarAlerta(mensagem, tipo) {
            console.log(`Alerta ${tipo}: ${mensagem}`);
            
            const container = document.getElementById('alertContainer');
            if (!container) {
                alert(mensagem);
                return;
            }
            
            const alerta = document.createElement('div');
            alerta.className = `alert alert-${tipo}`;
            alerta.textContent = mensagem;
            
            container.innerHTML = '';
            container.appendChild(alerta);
            
            setTimeout(() => {
                if (alerta.parentNode) {
                    alerta.remove();
                }
            }, 5000);
        }
        
        // Carregar dashboard
        function carregarDashboard() {
            console.log('Carregando dashboard...');
            
            try {
                if (metricas && metricas.resumoGeral) {
                    document.getElementById('totalProjetos').textContent = metricas.resumoGeral.totalProjetos || 0;
                    document.getElementById('orcamentoTotal').textContent = 'R$ ' + (metricas.resumoGeral.orcamentoTotal || 0).toLocaleString('pt-BR');
                    document.getElementById('progressoMedio').textContent = (metricas.resumoGeral.progressoMedio || 0) + '%';
                }
                
                if (metricas && metricas.statusDistribuicao) {
                    carregarStatus();
                }
                
                atualizarDebugInfo();
                console.log('Dashboard carregado');
                
            } catch (error) {
                console.error('Erro ao carregar dashboard:', error);
                mostrarAlerta('Erro ao carregar dashboard: ' + error.message, 'error');
            }
        }
        
        // Carregar status
        function carregarStatus() {
            const container = document.getElementById('statusContainer');
            container.innerHTML = '';
            
            Object.entries(metricas.statusDistribuicao).forEach(([status, info]) => {
                const item = document.createElement('div');
                item.className = 'status-item';
                item.innerHTML = `
                    <span><strong>${status}</strong></span>
                    <span>${info.quantidade} (${info.percentual}%)</span>
                `;
                container.appendChild(item);
            });
        }
    </script>
</body>
</html>
