<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Seminário Final do Edital FACEPE N° 07/2024 [COMPET Residência Tecnológica]</title>
    
    <!-- Chart.js Library -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .header h1 {
            color: #1f4e79;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .link-area {
            border: 3px dashed #1976d2;
            border-radius: 15px;
            padding: 40px 20px;
            background: #f8f9ff;
            text-align: center;
        }
        
        .link-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #1976d2;
            border-radius: 10px;
            font-size: 16px;
            margin-bottom: 15px;
        }
        
        .btn {
            background: linear-gradient(45deg, #1976d2, #42a5f5);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            margin: 10px;
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .btn-success {
            background: linear-gradient(45deg, #4caf50, #81c784);
        }
        
        .columns-selection {
            display: none;
        }
        
        .columns-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .column-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s ease;
        }
        
        .column-card.selected {
            border-color: #1976d2;
            background: #e3f2fd;
        }
        
        .column-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .column-checkbox {
            width: 20px;
            height: 20px;
            margin-right: 15px;
            cursor: pointer;
        }
        
        .column-name {
            font-weight: bold;
            color: #1f4e79;
            font-size: 1.1rem;
            flex: 1;
        }
        
        .column-letter {
            background: #1976d2;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9rem;
            font-weight: bold;
        }
        
        .column-preview {
            background: white;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #e0e0e0;
            margin-top: 10px;
            font-size: 0.9rem;
            color: #666;
            max-height: 100px;
            overflow-y: auto;
        }
        
        .column-sample {
            margin: 5px 0;
            padding: 3px 8px;
            background: #f5f5f5;
            border-radius: 3px;
            font-family: monospace;
        }
        
        .selection-summary {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #4caf50;
        }
        
        .selection-summary h4 {
            color: #2e7d32;
            margin-bottom: 10px;
        }
        
        .selected-columns {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .selected-tag {
            background: #4caf50;
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .dashboard {
            display: none;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .card-icon {
            font-size: 2rem;
            margin-right: 15px;
        }
        
        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1f4e79;
        }
        
        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 5px;
        }
        
        .metric-label {
            color: #666;
            font-size: 1rem;
        }
        
        .filter-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 6px;
            font-weight: 500;
            font-size: 0.9rem;
        }
        
        .chart-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .chart-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1f4e79;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 500;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #1976d2;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .info-box {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #1976d2;
        }
        
        .info-box h4 {
            color: #1976d2;
            margin-bottom: 10px;
        }
        
        .select-all-btn {
            background: #ff9800;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            margin: 10px 5px;
            font-weight: 500;
        }
        
        .clear-all-btn {
            background: #f44336;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            margin: 10px 5px;
            font-weight: 500;
        }
        
        .columns-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .columns-count {
            color: #666;
            font-size: 1rem;
        }

        /* Logo Strip */
        .logo-strip {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 30px;
            margin-bottom: 20px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .logo-item {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo-item img {
            max-height: 60px;
            max-width: 150px;
            object-fit: contain;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }

        /* Cross Analysis Section */
        .cross-analysis {
            background: #fff3e0;
            border: 2px solid #ff9800;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
        }

        .cross-analysis h4 {
            color: #e65100;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .cross-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }

        .cross-option {
            background: white;
            border: 2px solid #ffcc02;
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .cross-option:hover {
            border-color: #ff9800;
            background: #fff8e1;
        }

        .cross-option.selected {
            border-color: #e65100;
            background: #ffecb3;
        }

        .cross-option input[type="radio"] {
            margin-right: 10px;
        }

        .cross-option-title {
            font-weight: bold;
            color: #e65100;
            margin-bottom: 5px;
        }

        .cross-option-desc {
            font-size: 0.9rem;
            color: #666;
        }

        /* Logo Strip */
        .logo-strip {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 30px;
            margin-bottom: 20px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .logo-item {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo-item img {
            max-height: 60px;
            max-width: 150px;
            object-fit: contain;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }

        /* Cross Analysis Section */
        .cross-analysis {
            background: #fff3e0;
            border: 2px solid #ff9800;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
        }

        .cross-analysis h4 {
            color: #e65100;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .cross-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }

        .cross-option {
            background: white;
            border: 2px solid #ffcc02;
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .cross-option:hover {
            border-color: #ff9800;
            background: #fff8e1;
        }

        .cross-option.selected {
            border-color: #e65100;
            background: #ffecb3;
        }

        .cross-option input[type="radio"] {
            margin-right: 10px;
        }

        .cross-option-title {
            font-weight: bold;
            color: #e65100;
            margin-bottom: 5px;
        }

        .cross-option-desc {
            font-size: 0.9rem;
            color: #666;
        }

        .cross-columns-selector {
            display: none;
            margin-top: 20px;
            padding: 20px;
            background: white;
            border-radius: 10px;
            border: 2px solid #ffcc02;
        }

        .cross-columns-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }

        .cross-column-item {
            display: flex;
            align-items: center;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 8px;
            border: 1px solid #ddd;
        }

        .cross-column-item input[type="checkbox"] {
            margin-right: 10px;
        }

        .cross-column-item.selected {
            background: #e3f2fd;
            border-color: #1976d2;
        }

        /* Export Buttons */
        .export-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 140px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .export-pdf {
            background: linear-gradient(45deg, #e53e3e, #fc8181);
            color: white;
        }

        .export-pdf:hover {
            background: linear-gradient(45deg, #c53030, #e53e3e);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(229, 62, 62, 0.3);
        }

        .export-word {
            background: linear-gradient(45deg, #3182ce, #63b3ed);
            color: white;
        }

        .export-word:hover {
            background: linear-gradient(45deg, #2c5282, #3182ce);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(49, 130, 206, 0.3);
        }

        .export-excel {
            background: linear-gradient(45deg, #38a169, #68d391);
            color: white;
        }

        .export-excel:hover {
            background: linear-gradient(45deg, #2f855a, #38a169);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(56, 161, 105, 0.3);
        }

        .export-excel {
            background: linear-gradient(45deg, #38a169, #68d391);
            color: white;
        }

        .export-excel:hover {
            background: linear-gradient(45deg, #2f855a, #38a169);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(56, 161, 105, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <!-- Logo Strip -->
            <div class="logo-strip">
                <div class="logo-item">
                    <img src="https://i.postimg.cc/V6hswvLy/CTI-adaptada-fundo-transparente.png" alt="CTI">
                </div>
                <div class="logo-item">
                    <img src="https://i.postimg.cc/9D37G8cB/Marca-FACEPE.png" alt="FACEPE">
                </div>
            </div>

            <h1>📊 Dashboard de Projetos FACEPE</h1>
            <p>Seminário Final do Edital FACEPE N° 07/2024 [COMPET Residência Tecnológica]</p>
        </div>
        
        <!-- Link Section -->
        <div id="linkSection" class="section">
            <div class="link-area">
                <h3 style="color: #1976d2; margin-bottom: 20px;">🔗 Conectar Planilha</h3>
                <p style="color: #666; margin-bottom: 20px;">
                    Cole o link da sua planilha e escolha quais colunas usar para o dashboard
                </p>
                <input type="url" id="linkInput" class="link-input" 
                       placeholder="Cole o link da sua planilha Google Sheets..." />
                <button class="btn" onclick="conectarPlanilha()" id="btnConectar">
                    🔍 Conectar e Ver Colunas
                </button>
            </div>
            <div id="alertContainer"></div>
        </div>
        
        <!-- Columns Selection -->
        <div id="columnsSection" class="section columns-selection">
            <div class="columns-header">
                <h3 style="color: #1f4e79;">🎯 Escolha as Colunas para o Dashboard</h3>
                <div class="columns-count" id="columnsCount">0 colunas encontradas</div>
            </div>
            
            <div class="info-box">
                <h4>📋 Instruções:</h4>
                <p>✅ <strong>Marque as colunas</strong> que você quer usar no dashboard</p>
                <p>📊 <strong>Mínimo 2 colunas</strong> para gerar gráficos</p>
                <p>🎨 <strong>Mais colunas = mais filtros</strong> e análises</p>
            </div>
            
            <div style="text-align: center; margin: 20px 0;">
                <button class="select-all-btn" onclick="selecionarTodas()">
                    ✅ Selecionar Todas
                </button>
                <button class="clear-all-btn" onclick="limparSelecao()">
                    ❌ Limpar Seleção
                </button>
            </div>
            
            <div id="columnsGrid" class="columns-grid">
                <!-- Colunas serão inseridas aqui -->
            </div>
            
            <div id="selectionSummary" class="selection-summary" style="display: none;">
                <h4>📊 Colunas Selecionadas:</h4>
                <div id="selectedColumns" class="selected-columns"></div>
            </div>
            
            <!-- Cross Analysis Section -->
            <div id="crossAnalysisSection" class="cross-analysis" style="display: none;">
                <h4>🔄 Análise Cruzada - Gráficos Comparativos</h4>
                <p style="color: #666; margin-bottom: 20px;">
                    Escolha como quer analisar os dados das colunas selecionadas
                </p>

                <div class="cross-options">
                    <div class="cross-option" onclick="selecionarTipoAnalise('individual')">
                        <input type="radio" name="analysisType" value="individual" id="individual" checked>
                        <div class="cross-option-title">📊 Análise Individual</div>
                        <div class="cross-option-desc">Um gráfico para cada coluna selecionada</div>
                    </div>

                    <div class="cross-option" onclick="selecionarTipoAnalise('cross')">
                        <input type="radio" name="analysisType" value="cross" id="cross">
                        <div class="cross-option-title">🔄 Análise Cruzada</div>
                        <div class="cross-option-desc">Gráficos comparando 2 ou mais colunas</div>
                    </div>

                    <div class="cross-option" onclick="selecionarTipoAnalise('both')">
                        <input type="radio" name="analysisType" value="both" id="both">
                        <div class="cross-option-title">📈 Análise Completa</div>
                        <div class="cross-option-desc">Gráficos individuais + comparativos</div>
                    </div>
                </div>

                <div id="crossColumnsSelector" class="cross-columns-selector">
                    <h5 style="color: #e65100; margin-bottom: 15px;">
                        🎯 Selecione as colunas para comparar:
                    </h5>
                    <p style="color: #666; font-size: 0.9rem; margin-bottom: 15px;">
                        Marque 2 ou mais colunas para gerar gráficos comparativos
                    </p>
                    <div id="crossColumnsGrid" class="cross-columns-grid">
                        <!-- Colunas para análise cruzada serão inseridas aqui -->
                    </div>
                </div>
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <button class="btn btn-success" onclick="gerarDashboard()" id="btnGerar" disabled>
                    🚀 Gerar Dashboard
                </button>
            </div>
        </div>

        <!-- Dashboard Section -->
        <div id="dashboardSection" class="section dashboard">
            <h3 style="color: #1f4e79; margin-bottom: 30px; text-align: center;">
                📊 Dashboard Gerado
            </h3>

            <!-- Metrics Cards -->
            <div class="dashboard-grid">
                <div class="card">
                    <div class="card-header">
                        <div class="card-icon">📊</div>
                        <div class="card-title">Total de Registros</div>
                    </div>
                    <div class="metric-value" id="totalRegistros">0</div>
                    <div class="metric-label">registros encontrados</div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <div class="card-icon">📋</div>
                        <div class="card-title">Colunas Analisadas</div>
                    </div>
                    <div class="metric-value" id="totalColunas">0</div>
                    <div class="metric-label">colunas selecionadas</div>
                </div>

                <!-- Botões de Exportação -->
                <div class="card">
                    <div class="card-header">
                        <div class="card-icon">📤</div>
                        <div class="card-title">Exportar Relatório</div>
                    </div>
                    <div style="display: flex; gap: 10px; justify-content: center; padding: 10px; flex-wrap: wrap;">
                        <button onclick="exportarPDF()" class="export-btn export-pdf">
                            📄 Exportar PDF
                        </button>
                        <button onclick="exportarWord()" class="export-btn export-word">
                            📝 Exportar Word
                        </button>
                        <button onclick="exportarExcel()" class="export-btn export-excel">
                            📊 Exportar Excel
                        </button>
                    </div>
                </div>

                <div id="filtrosContainer" class="card">
                    <div class="card-header">
                        <div class="card-icon">🎯</div>
                        <div class="card-title">Filtros Disponíveis</div>
                    </div>
                    <div id="filtrosList"></div>
                </div>
            </div>

            <!-- Charts Container -->
            <div id="chartsContainer">
                <!-- Gráficos serão inseridos dinamicamente -->
            </div>

            <!-- Botão para nova conexão -->
            <div style="text-align: center; margin-top: 30px;">
                <button class="btn" onclick="novaConexao()">
                    🔄 Conectar Nova Planilha
                </button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // Variáveis globais
        let dadosBrutos = [];
        let colunasSelecionadas = [];
        let dadosProcessados = [];
        let graficosGerados = {};
        let tipoAnalise = 'individual';
        let colunasCruzadas = [];

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Dashboard flexível carregado');

            const linkInput = document.getElementById('linkInput');
            if (linkInput) {
                linkInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        conectarPlanilha();
                    }
                });
            }
        });

        // Conectar planilha
        function conectarPlanilha() {
            const link = document.getElementById('linkInput').value.trim();

            if (!link) {
                mostrarAlerta('Por favor, insira o link da planilha', 'error');
                return;
            }

            const btnConectar = document.getElementById('btnConectar');
            btnConectar.disabled = true;
            btnConectar.innerHTML = '<span class="loading"></span> Conectando...';

            google.script.run
                .withSuccessHandler(function(resultado) {
                    btnConectar.disabled = false;
                    btnConectar.innerHTML = '🔍 Conectar e Ver Colunas';

                    if (resultado.sucesso) {
                        mostrarAlerta('Planilha conectada! Escolha as colunas para o dashboard.', 'success');

                        // Armazenar dados
                        dadosBrutos = resultado.dadosBrutos;

                        console.log(`🔥 DADOS RECEBIDOS DO SERVIDOR:`);
                        console.log(`📊 Total de linhas brutas: ${dadosBrutos.length}`);
                        console.log(`📋 Cabeçalhos: ${dadosBrutos[0]}`);
                        console.log(`📈 Linhas de dados: ${dadosBrutos.length - 1}`);

                        // Mostrar colunas para seleção
                        mostrarColunasParaSelecao(resultado.colunas, resultado.dadosBrutos);

                        // Mostrar seção de seleção
                        mostrarSelecaoColunas();

                    } else {
                        mostrarAlerta(resultado.mensagem, 'error');
                    }
                })
                .withFailureHandler(function(erro) {
                    btnConectar.disabled = false;
                    btnConectar.innerHTML = '🔍 Conectar e Ver Colunas';
                    mostrarAlerta('Erro ao conectar: ' + erro.message, 'error');
                })
                .obterDadosParaMapeamento(link);
        }

        // Mostrar colunas para seleção
        function mostrarColunasParaSelecao(colunas, dados) {
            const container = document.getElementById('columnsGrid');
            const countEl = document.getElementById('columnsCount');

            container.innerHTML = '';
            countEl.textContent = `${colunas.length} colunas encontradas | ${dados.length - 1} registros carregados`;

            console.log(`📊 Processando ${colunas.length} colunas com ${dados.length - 1} registros TOTAIS`);

            colunas.forEach((coluna, index) => {
                const card = document.createElement('div');
                card.className = 'column-card';
                card.id = `column-${index}`;

                // Obter amostras de dados desta coluna
                const amostras = obterAmostrasDados(dados, index);
                const totalValores = contarValoresUnicos(dados, index);

                card.innerHTML = `
                    <div class="column-header">
                        <input type="checkbox" class="column-checkbox" id="check-${index}"
                               onchange="toggleColumn(${index})">
                        <div class="column-name">${coluna}</div>
                        <div class="column-letter">Col ${String.fromCharCode(65 + index)}</div>
                    </div>
                    <div class="column-preview">
                        <strong>📊 ${totalValores} valores únicos:</strong>
                        ${amostras.map(amostra => `<div class="column-sample">${amostra}</div>`).join('')}
                        ${dados.length > 4 ? '<div style="color: #666; font-size: 0.8rem; margin-top: 5px;">... e mais dados</div>' : ''}
                    </div>
                `;

                container.appendChild(card);
            });
        }

        // Contar valores únicos em uma coluna - TODAS AS LINHAS
        function contarValoresUnicos(dados, colunaIndex) {
            const valoresUnicos = new Set();

            // PROCESSAR TODAS AS LINHAS (incluindo vazias)
            for (let i = 1; i < dados.length; i++) {
                const linha = dados[i];
                const valor = linha && linha[colunaIndex] ? linha[colunaIndex].toString().trim() : 'Vazio';
                valoresUnicos.add(valor);
            }

            return valoresUnicos.size;
        }

        // Obter amostras de dados de uma coluna - TODAS AS LINHAS
        function obterAmostrasDados(dados, colunaIndex) {
            const amostras = [];
            const maxAmostras = 3;

            // PROCESSAR TODAS AS LINHAS (incluindo vazias)
            for (let i = 1; i < Math.min(dados.length, maxAmostras + 1); i++) {
                const linha = dados[i];
                let valor = linha && linha[colunaIndex] ? linha[colunaIndex].toString().trim() : '(vazio)';

                if (valor.length > 30) {
                    valor = valor.substring(0, 30) + '...';
                }

                amostras.push(valor);
            }

            return amostras;
        }

        // Toggle seleção de coluna
        function toggleColumn(index) {
            const checkbox = document.getElementById(`check-${index}`);
            const card = document.getElementById(`column-${index}`);

            if (checkbox.checked) {
                card.classList.add('selected');
                if (!colunasSelecionadas.includes(index)) {
                    colunasSelecionadas.push(index);
                }
            } else {
                card.classList.remove('selected');
                colunasSelecionadas = colunasSelecionadas.filter(i => i !== index);
            }

            atualizarResumoSelecao();
            verificarSelecao();
            atualizarAnalisecruzada();
        }

        // Selecionar todas as colunas
        function selecionarTodas() {
            const checkboxes = document.querySelectorAll('.column-checkbox');
            colunasSelecionadas = [];

            checkboxes.forEach((checkbox, index) => {
                checkbox.checked = true;
                document.getElementById(`column-${index}`).classList.add('selected');
                colunasSelecionadas.push(index);
            });

            atualizarResumoSelecao();
            verificarSelecao();
            atualizarAnalisecruzada();
        }

        // Limpar seleção
        function limparSelecao() {
            const checkboxes = document.querySelectorAll('.column-checkbox');
            colunasSelecionadas = [];

            checkboxes.forEach((checkbox, index) => {
                checkbox.checked = false;
                document.getElementById(`column-${index}`).classList.remove('selected');
            });

            atualizarResumoSelecao();
            verificarSelecao();
            atualizarAnalisecruzada();
        }

        // Atualizar resumo da seleção
        function atualizarResumoSelecao() {
            const summaryDiv = document.getElementById('selectionSummary');
            const selectedDiv = document.getElementById('selectedColumns');

            if (colunasSelecionadas.length === 0) {
                summaryDiv.style.display = 'none';
                return;
            }

            summaryDiv.style.display = 'block';
            selectedDiv.innerHTML = '';

            colunasSelecionadas.forEach(index => {
                const coluna = dadosBrutos[0][index];
                const tag = document.createElement('div');
                tag.className = 'selected-tag';
                tag.textContent = `${coluna} (${String.fromCharCode(65 + index)})`;
                selectedDiv.appendChild(tag);
            });
        }

        // Verificar seleção
        function verificarSelecao() {
            const btnGerar = document.getElementById('btnGerar');
            const minColunas = 2;

            if (colunasSelecionadas.length >= minColunas) {
                btnGerar.disabled = false;
                btnGerar.style.background = 'linear-gradient(45deg, #4caf50, #81c784)';
                btnGerar.textContent = `🚀 Gerar Dashboard (${colunasSelecionadas.length} colunas)`;
            } else {
                btnGerar.disabled = true;
                btnGerar.style.background = '#ccc';
                btnGerar.textContent = `🚀 Selecione pelo menos ${minColunas} colunas`;
            }
        }

        // Atualizar seção de análise cruzada
        function atualizarAnalisecruzada() {
            const crossSection = document.getElementById('crossAnalysisSection');

            if (colunasSelecionadas.length >= 2) {
                crossSection.style.display = 'block';
                preencherColunasCruzadas();
            } else {
                crossSection.style.display = 'none';
            }
        }

        // Preencher colunas para análise cruzada
        function preencherColunasCruzadas() {
            const container = document.getElementById('crossColumnsGrid');
            const cabecalhos = dadosBrutos[0];

            container.innerHTML = '';

            colunasSelecionadas.forEach(colIndex => {
                const nomeColuna = cabecalhos[colIndex];

                const item = document.createElement('div');
                item.className = 'cross-column-item';
                item.innerHTML = `
                    <input type="checkbox" id="cross-${colIndex}" onchange="toggleCrossColumn(${colIndex})">
                    <label for="cross-${colIndex}">${nomeColuna}</label>
                `;

                container.appendChild(item);
            });
        }

        // Selecionar tipo de análise
        function selecionarTipoAnalise(tipo) {
            tipoAnalise = tipo;

            // Atualizar visual dos radio buttons
            document.querySelectorAll('.cross-option').forEach(option => {
                option.classList.remove('selected');
            });

            const selectedOption = document.querySelector(`input[value="${tipo}"]`).closest('.cross-option');
            selectedOption.classList.add('selected');

            // Mostrar/ocultar seletor de colunas cruzadas
            const crossSelector = document.getElementById('crossColumnsSelector');
            if (tipo === 'cross' || tipo === 'both') {
                crossSelector.style.display = 'block';
            } else {
                crossSelector.style.display = 'none';
            }
        }

        // Toggle coluna para análise cruzada
        function toggleCrossColumn(index) {
            const checkbox = document.getElementById(`cross-${index}`);
            const item = checkbox.closest('.cross-column-item');

            if (checkbox.checked) {
                item.classList.add('selected');
                if (!colunasCruzadas.includes(index)) {
                    colunasCruzadas.push(index);
                }
            } else {
                item.classList.remove('selected');
                colunasCruzadas = colunasCruzadas.filter(i => i !== index);
            }
        }

        // Gerar dashboard
        function gerarDashboard() {
            if (colunasSelecionadas.length < 2) {
                mostrarAlerta('Selecione pelo menos 2 colunas', 'error');
                return;
            }

            const btnGerar = document.getElementById('btnGerar');
            btnGerar.disabled = true;
            btnGerar.innerHTML = '<span class="loading"></span> Gerando Dashboard...';

            // Processar dados localmente
            processarDadosLocalmente();

            // Mostrar dashboard
            setTimeout(() => {
                btnGerar.disabled = false;
                btnGerar.innerHTML = '🚀 Gerar Dashboard';
                mostrarAlerta('Dashboard gerado com sucesso!', 'success');
                mostrarDashboard();
            }, 1000);
        }

        // Processar dados localmente - TODAS AS LINHAS
        function processarDadosLocalmente() {
            console.log('🔄 Processando TODAS as linhas com colunas selecionadas:', colunasSelecionadas);
            console.log(`📊 Total de linhas a processar: ${dadosBrutos.length - 1}`);

            const cabecalhos = dadosBrutos[0];
            dadosProcessados = [];

            // Processar CADA LINHA (SEM FILTRAR NADA)
            for (let i = 1; i < dadosBrutos.length; i++) {
                const linha = dadosBrutos[i];

                // PROCESSAR TODAS AS LINHAS - não verificar se está vazia
                const registro = {};

                colunasSelecionadas.forEach(colIndex => {
                    const nomeColuna = cabecalhos[colIndex];
                    registro[nomeColuna] = linha[colIndex] || '';
                });

                dadosProcessados.push(registro);
            }

            console.log(`✅ ${dadosProcessados.length} registros processados de ${dadosBrutos.length - 1} linhas totais`);

            // Verificar se perdemos alguma linha
            if (dadosProcessados.length !== dadosBrutos.length - 1) {
                console.warn(`⚠️ ATENÇÃO: ${(dadosBrutos.length - 1) - dadosProcessados.length} linhas foram filtradas (provavelmente vazias)`);
            } else {
                console.log(`🎯 PERFEITO: Todas as ${dadosProcessados.length} linhas foram processadas!`);
            }

            // Gerar análises para cada coluna selecionada
            gerarAnalisesPorColuna();
        }

        // Gerar análises por coluna - TODAS AS LINHAS
        function gerarAnalisesPorColuna() {
            graficosGerados = {};
            const cabecalhos = dadosBrutos[0];

            console.log(`🔄 Gerando análises para ${colunasSelecionadas.length} colunas`);

            colunasSelecionadas.forEach(colIndex => {
                const nomeColuna = cabecalhos[colIndex];

                // PROCESSAR TODAS AS LINHAS (não filtrar valores únicos)
                const contagem = {};
                let totalLinhasProcessadas = 0;

                // Percorrer TODAS as linhas de dados processados
                dadosProcessados.forEach(registro => {
                    const valor = registro[nomeColuna];
                    const valorLimpo = valor ? valor.toString().trim() : 'Vazio';

                    // CONTAR TODAS AS OCORRÊNCIAS (incluindo repetidas)
                    contagem[valorLimpo] = (contagem[valorLimpo] || 0) + 1;
                    totalLinhasProcessadas++;
                });

                graficosGerados[nomeColuna] = contagem;

                console.log(`📊 ${nomeColuna}: ${totalLinhasProcessadas} linhas processadas, ${Object.keys(contagem).length} valores diferentes`);
            });

            console.log(`✅ Análises geradas para todas as ${dadosProcessados.length} linhas`);
        }

        // Mostrar seções
        function mostrarSelecaoColunas() {
            document.getElementById('linkSection').style.display = 'none';
            document.getElementById('columnsSection').style.display = 'block';
            document.getElementById('dashboardSection').style.display = 'none';
        }

        function mostrarDashboard() {
            document.getElementById('linkSection').style.display = 'none';
            document.getElementById('columnsSection').style.display = 'none';
            document.getElementById('dashboardSection').style.display = 'block';

            setTimeout(() => {
                carregarDashboard();
            }, 100);
        }

        // Nova conexão
        function novaConexao() {
            if (confirm('Deseja conectar uma nova planilha?')) {
                location.reload();
            }
        }

        // Mostrar alerta
        function mostrarAlerta(mensagem, tipo) {
            const container = document.getElementById('alertContainer');
            if (!container) {
                alert(mensagem);
                return;
            }

            const alerta = document.createElement('div');
            alerta.className = `alert alert-${tipo}`;
            alerta.textContent = mensagem;

            container.innerHTML = '';
            container.appendChild(alerta);

            setTimeout(() => {
                if (alerta.parentNode) {
                    alerta.remove();
                }
            }, 5000);
        }

        // Carregar dashboard
        function carregarDashboard() {
            console.log('🔄 Carregando dashboard flexível...');
            console.log(`📊 Dados processados: ${dadosProcessados.length} registros`);
            console.log(`📋 Colunas selecionadas: ${colunasSelecionadas.length}`);

            try {
                // Métricas básicas
                document.getElementById('totalRegistros').textContent = dadosProcessados.length;
                document.getElementById('totalColunas').textContent = colunasSelecionadas.length;

                // Verificar se temos dados suficientes
                if (dadosProcessados.length === 0) {
                    mostrarAlerta('Nenhum dado foi processado. Verifique se a planilha tem dados válidos.', 'error');
                    return;
                }

                // Carregar filtros
                carregarFiltros();

                // Gerar gráficos
                gerarGraficos();

                console.log(`✅ Dashboard carregado com ${dadosProcessados.length} registros`);

            } catch (error) {
                console.error('❌ Erro ao carregar dashboard:', error);
                mostrarAlerta('Erro ao carregar dashboard: ' + error.message, 'error');
            }
        }

        // Carregar filtros
        function carregarFiltros() {
            const container = document.getElementById('filtrosList');
            container.innerHTML = '';

            const cabecalhos = dadosBrutos[0];

            colunasSelecionadas.slice(0, 5).forEach(colIndex => { // Mostrar apenas os primeiros 5
                const nomeColuna = cabecalhos[colIndex];
                const contagem = graficosGerados[nomeColuna];
                const totalValores = Object.keys(contagem).length;

                // Calcular total de registros para esta coluna
                const totalRegistros = Object.values(contagem).reduce((a, b) => a + b, 0);

                const item = document.createElement('div');
                item.className = 'filter-item';
                item.style.backgroundColor = gerarCorAleatoria();
                item.style.color = '#ffffff';

                item.innerHTML = `
                    <span>${nomeColuna}</span>
                    <span>${totalRegistros} registros | ${totalValores} valores únicos</span>
                `;

                container.appendChild(item);
            });
        }

        // Gerar gráficos
        function gerarGraficos() {
            const chartsContainer = document.getElementById('chartsContainer');
            chartsContainer.innerHTML = '';

            const cabecalhos = dadosBrutos[0];

            // Gráficos individuais
            if (tipoAnalise === 'individual' || tipoAnalise === 'both') {
                colunasSelecionadas.forEach((colIndex, index) => {
                    const nomeColuna = cabecalhos[colIndex];
                    const dados = graficosGerados[nomeColuna];

                    if (Object.keys(dados).length > 0) {
                        criarGrafico(nomeColuna, dados, `individual-${index}`);
                    }
                });
            }

            // Gráficos cruzados
            if ((tipoAnalise === 'cross' || tipoAnalise === 'both') && colunasCruzadas.length >= 2) {
                gerarGraficosCruzados();
            }
        }

        // Gerar gráficos cruzados inteligentes
        function gerarGraficosCruzados() {
            const cabecalhos = dadosBrutos[0];

            if (colunasCruzadas.length >= 2) {
                // 1. Matriz de distribuição cruzada
                criarMatrizDistribuicao();

                // 2. Gráficos de insights por pares
                for (let i = 0; i < colunasCruzadas.length - 1; i++) {
                    for (let j = i + 1; j < colunasCruzadas.length; j++) {
                        const col1Index = colunasCruzadas[i];
                        const col2Index = colunasCruzadas[j];
                        const col1Nome = cabecalhos[col1Index];
                        const col2Nome = cabecalhos[col2Index];

                        criarGraficoInsights(col1Nome, col2Nome, col1Index, col2Index);
                    }
                }

                // 3. Análise de concentração (se mais de 2 colunas)
                if (colunasCruzadas.length >= 3) {
                    criarAnaliseConcentracao();
                }

                // 4. Ranking de combinações mais frequentes
                criarRankingCombinacoesFrequentes();
            }
        }

        // 1. Criar matriz de distribuição cruzada
        function criarMatrizDistribuicao() {
            const chartsContainer = document.getElementById('chartsContainer');
            const cabecalhos = dadosBrutos[0];

            const chartDiv = document.createElement('div');
            chartDiv.className = 'chart-container';
            chartDiv.innerHTML = `
                <div class="chart-title">� Matriz de Distribuição Cruzada</div>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                    <strong>💡 Insight:</strong> Mostra como os valores se distribuem entre as colunas selecionadas
                </div>
                <canvas id="chart-matriz" width="400" height="300"></canvas>
            `;

            chartsContainer.appendChild(chartDiv);

            setTimeout(() => {
                try {
                    const canvas = document.getElementById('chart-matriz');
                    if (!canvas) return;

                    const ctx = canvas.getContext('2d');

                    // Calcular distribuição cruzada
                    const distribuicao = calcularDistribuicaoCruzada();

                    new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: distribuicao.labels,
                            datasets: distribuicao.datasets
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                legend: {
                                    position: 'top'
                                },
                                tooltip: {
                                    callbacks: {
                                        afterLabel: function(context) {
                                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                            const percentage = ((context.raw / total) * 100).toFixed(1);
                                            return `${percentage}% do total desta coluna`;
                                        }
                                    }
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    title: {
                                        display: true,
                                        text: 'Quantidade de Registros'
                                    }
                                },
                                x: {
                                    title: {
                                        display: true,
                                        text: 'Valores Únicos'
                                    }
                                }
                            }
                        }
                    });

                } catch (error) {
                    console.error('Erro ao criar matriz de distribuição:', error);
                }
            }, 100);
        }

        // Calcular distribuição cruzada inteligente
        function calcularDistribuicaoCruzada() {
            const cabecalhos = dadosBrutos[0];
            const datasets = [];
            const todasChaves = new Set();

            // Coletar todos os valores únicos
            colunasCruzadas.forEach(colIndex => {
                const nomeColuna = cabecalhos[colIndex];
                const valores = dadosProcessados.map(registro => registro[nomeColuna] || 'Não Definido');
                valores.forEach(valor => todasChaves.add(valor));
            });

            // Limitar a top 8 valores mais frequentes
            const valoresFrequentes = Array.from(todasChaves)
                .map(valor => ({
                    valor,
                    frequencia: dadosProcessados.filter(registro =>
                        colunasCruzadas.some(colIndex =>
                            registro[cabecalhos[colIndex]] === valor
                        )
                    ).length
                }))
                .sort((a, b) => b.frequencia - a.frequencia)
                .slice(0, 8)
                .map(item => item.valor);

            // Criar dataset para cada coluna
            colunasCruzadas.forEach((colIndex, index) => {
                const nomeColuna = cabecalhos[colIndex];
                const cor = gerarCorAleatoria();

                const data = valoresFrequentes.map(valor => {
                    return dadosProcessados.filter(registro =>
                        registro[nomeColuna] === valor
                    ).length;
                });

                datasets.push({
                    label: nomeColuna,
                    data: data,
                    backgroundColor: cor,
                    borderColor: cor,
                    borderWidth: 1
                });
            });

            return {
                labels: valoresFrequentes,
                datasets: datasets
            };
        }

        // Criar gráfico de correlação entre duas colunas
        function criarGraficoCorrelacao(col1Nome, col2Nome, col1Index, col2Index) {
            const chartsContainer = document.getElementById('chartsContainer');

            const chartDiv = document.createElement('div');
            chartDiv.className = 'chart-container';
            chartDiv.innerHTML = `
                <div class="chart-title">🔗 ${col1Nome} vs ${col2Nome}</div>
                <canvas id="chart-correlacao-${col1Index}-${col2Index}" width="400" height="300"></canvas>
            `;

            chartsContainer.appendChild(chartDiv);

            setTimeout(() => {
                try {
                    const canvas = document.getElementById(`chart-correlacao-${col1Index}-${col2Index}`);
                    if (!canvas) return;

                    const ctx = canvas.getContext('2d');

                    // Preparar dados de correlação
                    const correlacaoData = {};

                    dadosProcessados.forEach(registro => {
                        const val1 = registro[col1Nome] || 'Não Definido';
                        const val2 = registro[col2Nome] || 'Não Definido';
                        const chave = `${val1} + ${val2}`;

                        correlacaoData[chave] = (correlacaoData[chave] || 0) + 1;
                    });

                    const labels = Object.keys(correlacaoData).slice(0, 8); // Limitar para visualização
                    const data = labels.map(label => correlacaoData[label]);

                    new Chart(ctx, {
                        type: 'doughnut',
                        data: {
                            labels: labels,
                            datasets: [{
                                data: data,
                                backgroundColor: labels.map(() => gerarCorAleatoria()),
                                borderWidth: 2,
                                borderColor: '#ffffff'
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                legend: {
                                    position: 'bottom'
                                }
                            }
                        }
                    });

                } catch (error) {
                    console.error(`Erro ao criar gráfico de correlação ${col1Nome} vs ${col2Nome}:`, error);
                }
            }, 200);
        }

        // Criar gráfico individual
        function criarGrafico(nomeColuna, dados, index) {
            const chartsContainer = document.getElementById('chartsContainer');

            // Criar container do gráfico
            const chartDiv = document.createElement('div');
            chartDiv.className = 'chart-container';
            chartDiv.innerHTML = `
                <div class="chart-title">📊 ${nomeColuna}</div>
                <canvas id="chart-${index}" width="400" height="200"></canvas>
            `;

            chartsContainer.appendChild(chartDiv);

            // Criar gráfico
            setTimeout(() => {
                try {
                    const canvas = document.getElementById(`chart-${index}`);
                    if (!canvas) return;

                    const ctx = canvas.getContext('2d');
                    const labels = Object.keys(dados);
                    const values = Object.values(dados);

                    // Limitar a 10 itens para melhor visualização
                    const labelsLimitados = labels.slice(0, 10);
                    const valuesLimitados = values.slice(0, 10);

                    const tipoGrafico = labelsLimitados.length <= 5 ? 'pie' : 'bar';

                    new Chart(ctx, {
                        type: tipoGrafico,
                        data: {
                            labels: labelsLimitados,
                            datasets: [{
                                label: 'Quantidade',
                                data: valuesLimitados,
                                backgroundColor: tipoGrafico === 'pie' ?
                                    labelsLimitados.map(() => gerarCorAleatoria()) :
                                    '#1976d2',
                                borderColor: tipoGrafico === 'pie' ? '#ffffff' : '#1565c0',
                                borderWidth: 2
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                legend: {
                                    position: tipoGrafico === 'pie' ? 'bottom' : 'top'
                                }
                            },
                            scales: tipoGrafico === 'bar' ? {
                                y: {
                                    beginAtZero: true
                                }
                            } : {}
                        }
                    });

                } catch (error) {
                    console.error(`Erro ao criar gráfico ${nomeColuna}:`, error);
                }
            }, 100 * index);
        }

        // Gerar cor aleatória
        function gerarCorAleatoria() {
            const cores = [
                '#1976d2', '#388e3c', '#f57c00', '#d32f2f', '#7b1fa2',
                '#0288d1', '#689f38', '#fbc02d', '#e64a19', '#5e35b1',
                '#0097a7', '#8bc34a', '#ffc107', '#ff5722', '#9c27b0'
            ];
            return cores[Math.floor(Math.random() * cores.length)];
        }

        // 2. Criar gráfico de insights por pares
        function criarGraficoInsights(col1Nome, col2Nome, col1Index, col2Index) {
            const chartsContainer = document.getElementById('chartsContainer');

            const chartDiv = document.createElement('div');
            chartDiv.className = 'chart-container';
            chartDiv.innerHTML = `
                <div class="chart-title">🔗 ${col1Nome} × ${col2Nome}</div>
                <div style="background: #fff3e0; padding: 15px; border-radius: 8px; margin-bottom: 15px; border-left: 4px solid #ff9800;">
                    <strong>🎯 Insight:</strong> Análise de correlação e padrões entre estas duas dimensões
                </div>
                <canvas id="chart-insights-${col1Index}-${col2Index}" width="400" height="300"></canvas>
            `;

            chartsContainer.appendChild(chartDiv);

            setTimeout(() => {
                try {
                    const canvas = document.getElementById(`chart-insights-${col1Index}-${col2Index}`);
                    if (!canvas) return;

                    const ctx = canvas.getContext('2d');
                    const dadosCorrelacao = calcularCorrelacaoInteligente(col1Nome, col2Nome);

                    new Chart(ctx, {
                        type: 'scatter',
                        data: {
                            datasets: [{
                                label: `${col1Nome} vs ${col2Nome}`,
                                data: dadosCorrelacao.pontos,
                                backgroundColor: dadosCorrelacao.cores,
                                borderColor: '#1976d2',
                                borderWidth: 2,
                                pointRadius: 8,
                                pointHoverRadius: 12
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                legend: { display: false },
                                tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            return `${context.raw.label}: ${context.raw.count} registros`;
                                        }
                                    }
                                }
                            },
                            scales: {
                                x: {
                                    type: 'category',
                                    title: { display: true, text: col1Nome }
                                },
                                y: {
                                    type: 'category',
                                    title: { display: true, text: col2Nome }
                                }
                            }
                        }
                    });
                } catch (error) {
                    console.error(`Erro ao criar gráfico de insights ${col1Nome} vs ${col2Nome}:`, error);
                }
            }, 200);
        }

        // Calcular correlação inteligente
        function calcularCorrelacaoInteligente(col1Nome, col2Nome) {
            const correlacoes = {};
            const pontos = [];
            const cores = [];

            dadosProcessados.forEach(registro => {
                const val1 = registro[col1Nome] || 'Não Definido';
                const val2 = registro[col2Nome] || 'Não Definido';
                const chave = `${val1}|${val2}`;

                correlacoes[chave] = (correlacoes[chave] || 0) + 1;
            });

            // Converter para pontos do scatter plot
            Object.entries(correlacoes).forEach(([chave, count]) => {
                const [val1, val2] = chave.split('|');

                pontos.push({
                    x: val1,
                    y: val2,
                    count: count,
                    label: `${val1} + ${val2}`
                });

                // Cor baseada na frequência
                const intensidade = Math.min(count / 5, 1);
                cores.push(`rgba(25, 118, 210, ${0.3 + intensidade * 0.7})`);
            });

            return { pontos, cores };
        }

        // 3. Análise de concentração
        function criarAnaliseConcentracao() {
            const chartsContainer = document.getElementById('chartsContainer');
            const cabecalhos = dadosBrutos[0];

            const chartDiv = document.createElement('div');
            chartDiv.className = 'chart-container';
            chartDiv.innerHTML = `
                <div class="chart-title">🎯 Análise de Concentração</div>
                <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 15px; border-left: 4px solid #2196f3;">
                    <strong>📈 Insight:</strong> Identifica onde estão concentrados os dados e quais combinações são mais raras
                </div>
                <canvas id="chart-concentracao" width="400" height="300"></canvas>
            `;

            chartsContainer.appendChild(chartDiv);

            setTimeout(() => {
                try {
                    const canvas = document.getElementById('chart-concentracao');
                    if (!canvas) return;

                    const ctx = canvas.getContext('2d');
                    const concentracao = calcularConcentracao();

                    new Chart(ctx, {
                        type: 'radar',
                        data: {
                            labels: concentracao.labels,
                            datasets: concentracao.datasets
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                legend: { position: 'top' }
                            },
                            scales: {
                                r: {
                                    beginAtZero: true,
                                    title: { display: true, text: 'Diversidade de Valores' }
                                }
                            }
                        }
                    });
                } catch (error) {
                    console.error('Erro ao criar análise de concentração:', error);
                }
            }, 300);
        }

        // Calcular concentração
        function calcularConcentracao() {
            const cabecalhos = dadosBrutos[0];
            const labels = colunasCruzadas.map(colIndex => cabecalhos[colIndex]);

            const diversidade = colunasCruzadas.map(colIndex => {
                const nomeColuna = cabecalhos[colIndex];
                const valores = Object.keys(graficosGerados[nomeColuna] || {});
                return valores.length;
            });

            const uniformidade = colunasCruzadas.map(colIndex => {
                const nomeColuna = cabecalhos[colIndex];
                const valores = Object.values(graficosGerados[nomeColuna] || {});
                const media = valores.reduce((a, b) => a + b, 0) / valores.length;
                const variancia = valores.reduce((sum, val) => sum + Math.pow(val - media, 2), 0) / valores.length;
                return Math.max(0, 10 - Math.sqrt(variancia)); // Normalizar para 0-10
            });

            return {
                labels: labels,
                datasets: [
                    {
                        label: 'Diversidade de Valores',
                        data: diversidade,
                        backgroundColor: 'rgba(76, 175, 80, 0.2)',
                        borderColor: '#4caf50',
                        borderWidth: 2
                    },
                    {
                        label: 'Uniformidade da Distribuição',
                        data: uniformidade,
                        backgroundColor: 'rgba(255, 152, 0, 0.2)',
                        borderColor: '#ff9800',
                        borderWidth: 2
                    }
                ]
            };
        }

        // 4. Ranking de combinações frequentes
        function criarRankingCombinacoesFrequentes() {
            const chartsContainer = document.getElementById('chartsContainer');

            const chartDiv = document.createElement('div');
            chartDiv.className = 'chart-container';
            chartDiv.innerHTML = `
                <div class="chart-title">🏆 Top Combinações Mais Frequentes</div>
                <div style="background: #f3e5f5; padding: 15px; border-radius: 8px; margin-bottom: 15px; border-left: 4px solid #9c27b0;">
                    <strong>🔥 Insight:</strong> Mostra as combinações de valores que aparecem com mais frequência nos dados
                </div>
                <canvas id="chart-ranking" width="400" height="300"></canvas>
            `;

            chartsContainer.appendChild(chartDiv);

            setTimeout(() => {
                try {
                    const canvas = document.getElementById('chart-ranking');
                    if (!canvas) return;

                    const ctx = canvas.getContext('2d');
                    const ranking = calcularRankingCombinacoesFrequentes();

                    new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: ranking.labels,
                            datasets: [{
                                label: 'Frequência',
                                data: ranking.data,
                                backgroundColor: ranking.cores,
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            indexAxis: 'y', // Fazer barras horizontais
                            plugins: {
                                legend: { display: false },
                                tooltip: {
                                    callbacks: {
                                        afterLabel: function(context) {
                                            const total = ranking.data.reduce((a, b) => a + b, 0);
                                            const percentage = ((context.raw / total) * 100).toFixed(1);
                                            return `${percentage}% do total`;
                                        }
                                    }
                                }
                            },
                            scales: {
                                x: {
                                    beginAtZero: true,
                                    title: { display: true, text: 'Número de Ocorrências' }
                                },
                                y: {
                                    title: { display: true, text: 'Combinações' }
                                }
                            }
                        }
                    });
                } catch (error) {
                    console.error('Erro ao criar ranking de combinações:', error);
                }
            }, 400);
        }

        // Calcular ranking de combinações
        function calcularRankingCombinacoesFrequentes() {
            const cabecalhos = dadosBrutos[0];
            const combinacoes = {};

            dadosProcessados.forEach(registro => {
                const valores = colunasCruzadas.map(colIndex => {
                    const nomeColuna = cabecalhos[colIndex];
                    return registro[nomeColuna] || 'Não Definido';
                });

                const chave = valores.join(' + ');
                combinacoes[chave] = (combinacoes[chave] || 0) + 1;
            });

            const ranking = Object.entries(combinacoes)
                .sort((a, b) => b[1] - a[1])
                .slice(0, 10);

            return {
                labels: ranking.map(([combo]) => combo.length > 50 ? combo.substring(0, 47) + '...' : combo),
                data: ranking.map(([, freq]) => freq),
                cores: ranking.map(() => gerarCorAleatoria())
            };
        }

        // Exportar para PDF
        async function exportarPDF() {
            try {
                console.log('🔄 Iniciando exportação para PDF...');

                // Verificar se jsPDF está disponível
                if (typeof window.jspdf === 'undefined') {
                    throw new Error('Biblioteca jsPDF não carregada');
                }

                const { jsPDF } = window.jspdf;
                const doc = new jsPDF();

                // Adicionar logos (simulação - você pode substituir por imagens reais)
                doc.setFontSize(10);
                doc.setTextColor(100, 100, 100);
                doc.text('FACEPE - Fundação de Amparo à Ciência e Tecnologia do Estado de Pernambuco', 20, 15);
                doc.text('CTI - Centro de Tecnologias Estratégicas do Nordeste', 20, 25);

                // Título do relatório
                doc.setFontSize(18);
                doc.setTextColor(25, 118, 210);
                doc.text('📊 Dashboard de Projetos FACEPE', 20, 45);

                // Data do relatório
                doc.setFontSize(11);
                doc.setTextColor(100, 100, 100);
                doc.text(`Gerado em: ${new Date().toLocaleDateString('pt-BR')} às ${new Date().toLocaleTimeString('pt-BR')}`, 20, 55);

                // Métricas principais
                doc.setFontSize(14);
                doc.setTextColor(0, 0, 0);
                doc.text('Resumo Executivo', 20, 75);

                doc.setFontSize(11);
                doc.text(`Total de Registros: ${dadosProcessados.length}`, 25, 90);
                doc.text(`Colunas Analisadas: ${colunasSelecionadas.length}`, 25, 100);

                // Análise por coluna
                let yPosition = 115;
                doc.setFontSize(14);
                doc.text('📈 Análise Detalhada por Coluna', 20, yPosition);
                yPosition += 20;

                const cabecalhos = dadosBrutos[0];
                colunasSelecionadas.forEach((colIndex, index) => {
                    if (yPosition > 250) {
                        doc.addPage();
                        yPosition = 30;
                    }

                    const nomeColuna = cabecalhos[colIndex];
                    const contagem = graficosGerados[nomeColuna];
                    const totalValores = Object.keys(contagem).length;
                    const totalRegistros = Object.values(contagem).reduce((a, b) => a + b, 0);

                    doc.setFontSize(12);
                    doc.setTextColor(25, 118, 210);
                    doc.text(`${index + 1}. ${nomeColuna}`, 25, yPosition);
                    yPosition += 15;

                    doc.setTextColor(0, 0, 0);
                    doc.text(`   • Total de registros: ${totalRegistros}`, 30, yPosition);
                    yPosition += 10;
                    doc.text(`   • Valores únicos: ${totalValores}`, 30, yPosition);
                    yPosition += 15;

                    // Top 5 valores mais frequentes
                    const topValores = Object.entries(contagem)
                        .sort((a, b) => b[1] - a[1])
                        .slice(0, 5);

                    doc.text('   Top 5 valores mais frequentes:', 30, yPosition);
                    yPosition += 10;

                    topValores.forEach(([valor, freq]) => {
                        const valorTruncado = valor.length > 40 ? valor.substring(0, 37) + '...' : valor;
                        const percentual = ((freq / totalRegistros) * 100).toFixed(1);
                        doc.text(`     • ${valorTruncado}: ${freq} (${percentual}%)`, 35, yPosition);
                        yPosition += 8;
                    });

                    yPosition += 10;
                });

                // Rodapé
                const pageHeight = doc.internal.pageSize.height;
                doc.setFontSize(8);
                doc.setTextColor(100, 100, 100);
                doc.text('FACEPE - Fundação de Amparo à Ciência e Tecnologia do Estado de Pernambuco', 20, pageHeight - 20);
                doc.text('CTI - Centro de Tecnologias Estratégicas do Nordeste', 20, pageHeight - 10);

                // Salvar PDF
                const nomeArquivo = `Dashboard_FACEPE_${new Date().toISOString().split('T')[0]}.pdf`;
                doc.save(nomeArquivo);

                mostrarAlerta('📄 PDF exportado com sucesso!', 'success');
                console.log('✅ PDF exportado:', nomeArquivo);

            } catch (error) {
                console.error('❌ Erro ao exportar PDF:', error);

                let mensagemErro = 'Erro ao exportar PDF: ';
                if (error.message.includes('jsPDF')) {
                    mensagemErro += 'Biblioteca PDF não carregada. Recarregue a página.';
                } else {
                    mensagemErro += error.message;
                }

                mostrarAlerta(mensagemErro, 'error');
            }
        }

        // Exportar para Word
        function exportarWord() {
            try {
                console.log('🔄 Iniciando exportação para Word...');

                // Gerar conteúdo HTML para Word
                let htmlContent = `
                    <html>
                    <head>
                        <meta charset="utf-8">
                        <title>Dashboard de Projetos FACEPE</title>
                        <style>
                            body { font-family: Arial, sans-serif; margin: 40px; }
                            h1 { color: #1976d2; border-bottom: 3px solid #1976d2; padding-bottom: 10px; }
                            h2 { color: #333; margin-top: 30px; }
                            h3 { color: #1976d2; }
                            .metric { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
                            .column-analysis { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
                            .top-values { margin-left: 20px; }
                            .value-item { margin: 5px 0; }
                            .footer { margin-top: 50px; text-align: center; color: #666; font-size: 12px; }
                        </style>
                    </head>
                    <body>
                        <div style="text-align: center; margin-bottom: 30px;">
                            <img src="https://i.postimg.cc/9D37G8cB/Marca-FACEPE.png" alt="FACEPE" style="height: 60px; margin: 10px;">
                            <img src="https://i.postimg.cc/V6hswvLy/CTI-adaptada-fundo-transparente.png" alt="CTI" style="height: 60px; margin: 10px;">
                        </div>

                        <h1>📊 Dashboard de Projetos FACEPE</h1>
                        <p><strong>Data do Relatório:</strong> ${new Date().toLocaleDateString('pt-BR')} às ${new Date().toLocaleTimeString('pt-BR')}</p>

                        <h2>📈 Resumo Executivo</h2>
                        <div class="metric">
                            <strong>Total de Registros:</strong> ${dadosProcessados.length}<br>
                            <strong>Colunas Analisadas:</strong> ${colunasSelecionadas.length}
                        </div>

                        <h2>📊 Análise Detalhada por Coluna</h2>
                `;

                const cabecalhos = dadosBrutos[0];
                colunasSelecionadas.forEach((colIndex, index) => {
                    const nomeColuna = cabecalhos[colIndex];
                    const contagem = graficosGerados[nomeColuna];
                    const totalValores = Object.keys(contagem).length;
                    const totalRegistros = Object.values(contagem).reduce((a, b) => a + b, 0);

                    htmlContent += `
                        <div class="column-analysis">
                            <h3>${index + 1}. ${nomeColuna}</h3>
                            <p><strong>Total de registros:</strong> ${totalRegistros}</p>
                            <p><strong>Valores únicos:</strong> ${totalValores}</p>

                            <h4>Top 10 valores mais frequentes:</h4>
                            <div class="top-values">
                    `;

                    const topValores = Object.entries(contagem)
                        .sort((a, b) => b[1] - a[1])
                        .slice(0, 10);

                    topValores.forEach(([valor, freq]) => {
                        const percentual = ((freq / totalRegistros) * 100).toFixed(1);
                        htmlContent += `
                            <div class="value-item">
                                <strong>${valor}:</strong> ${freq} ocorrências (${percentual}%)
                            </div>
                        `;
                    });

                    htmlContent += `
                            </div>
                        </div>
                    `;
                });

                htmlContent += `
                        <div class="footer">
                            <p>Relatório gerado automaticamente pelo Dashboard FACEPE</p>
                            <p>CTI - Centro de Tecnologias Estratégicas do Nordeste</p>
                        </div>
                    </body>
                    </html>
                `;

                // Criar blob e download
                const blob = new Blob([htmlContent], { type: 'application/msword' });
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `Dashboard_FACEPE_${new Date().toISOString().split('T')[0]}.doc`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(url);

                mostrarAlerta('📝 Documento Word exportado com sucesso!', 'success');
                console.log('✅ Word exportado com sucesso');

            } catch (error) {
                console.error('❌ Erro ao exportar Word:', error);
                mostrarAlerta('Erro ao exportar Word: ' + error.message, 'error');
            }
        }

        // Exportar para Excel
        function exportarExcel() {
            try {
                console.log('🔄 Iniciando exportação para Excel...');

                // Criar dados para Excel
                const dadosExcel = [];

                // Cabeçalho com informações do relatório
                dadosExcel.push(['Dashboard de Projetos FACEPE']);
                dadosExcel.push(['FACEPE - Fundação de Amparo à Ciência e Tecnologia do Estado de Pernambuco']);
                dadosExcel.push(['CTI - Centro de Tecnologias Estratégicas do Nordeste']);
                dadosExcel.push([`Gerado em: ${new Date().toLocaleDateString('pt-BR')} às ${new Date().toLocaleTimeString('pt-BR')}`]);
                dadosExcel.push([]); // Linha vazia

                // Resumo executivo
                dadosExcel.push(['RESUMO EXECUTIVO']);
                dadosExcel.push(['Total de Registros', dadosProcessados.length]);
                dadosExcel.push(['Colunas Analisadas', colunasSelecionadas.length]);
                dadosExcel.push([]); // Linha vazia

                // Análise detalhada por coluna
                dadosExcel.push(['ANÁLISE DETALHADA POR COLUNA']);
                dadosExcel.push([]); // Linha vazia

                const cabecalhos = dadosBrutos[0];
                colunasSelecionadas.forEach((colIndex, index) => {
                    const nomeColuna = cabecalhos[colIndex];
                    const contagem = graficosGerados[nomeColuna];
                    const totalValores = Object.keys(contagem).length;
                    const totalRegistros = Object.values(contagem).reduce((a, b) => a + b, 0);

                    dadosExcel.push([`${index + 1}. ${nomeColuna}`]);
                    dadosExcel.push(['Total de registros', totalRegistros]);
                    dadosExcel.push(['Valores únicos', totalValores]);
                    dadosExcel.push([]); // Linha vazia

                    // Cabeçalho da tabela de valores
                    dadosExcel.push(['Valor', 'Frequência', 'Percentual']);

                    // Top valores mais frequentes
                    const topValores = Object.entries(contagem)
                        .sort((a, b) => b[1] - a[1])
                        .slice(0, 20); // Top 20 para Excel

                    topValores.forEach(([valor, freq]) => {
                        const percentual = ((freq / totalRegistros) * 100).toFixed(1) + '%';
                        dadosExcel.push([valor, freq, percentual]);
                    });

                    dadosExcel.push([]); // Linha vazia entre colunas
                });

                // Converter para CSV
                const csvContent = dadosExcel.map(row =>
                    row.map(cell => {
                        // Escapar aspas e adicionar aspas se necessário
                        const cellStr = String(cell || '');
                        if (cellStr.includes(',') || cellStr.includes('"') || cellStr.includes('\n')) {
                            return '"' + cellStr.replace(/"/g, '""') + '"';
                        }
                        return cellStr;
                    }).join(',')
                ).join('\n');

                // Adicionar BOM para UTF-8
                const BOM = '\uFEFF';
                const csvWithBOM = BOM + csvContent;

                // Criar blob e download
                const blob = new Blob([csvWithBOM], { type: 'application/vnd.ms-excel;charset=utf-8' });
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `Dashboard_FACEPE_${new Date().toISOString().split('T')[0]}.csv`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(url);

                mostrarAlerta('📊 Planilha Excel exportada com sucesso!', 'success');
                console.log('✅ Excel exportado com sucesso');

            } catch (error) {
                console.error('❌ Erro ao exportar Excel:', error);
                mostrarAlerta('Erro ao exportar Excel: ' + error.message, 'error');
            }
        }

        console.log('Script flexível carregado');
    </script>

    <!-- Biblioteca jsPDF para exportação PDF -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
</body>
</html>
