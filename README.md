# 📊 Dashboard de Projetos FACEPE - Google Apps Script

Sistema completo de dashboard para acompanhamento de projetos desenvolvido em Google Apps Script, que roda diretamente no Google Sheets sem necessidade de deploy externo.

## 🎯 Características Principais

- **100% Google Sheets**: Roda nativamente no ambiente Google Workspace
- **Importação Automática**: Lê dados do arquivo `data.txt` no Google Drive
- **Dashboards Interativos**: Métricas, gráficos e tabelas dinâmicas
- **Atualização Automática**: Triggers configuráveis para atualização periódica
- **Relatórios por Email**: Envio automático de relatórios
- **Interface Configurável**: Painel de configurações completo

## 📁 Estrutura dos Arquivos

```
📂 Projeto Dashboard
├── 📄 data.txt              # Arquivo de dados (formato pipe-separated)
├── 📄 Code.gs               # Código principal e configuração
├── 📄 Metricas.gs           # Cálculos de KPIs e métricas
├── 📄 Dashboard.gs          # Interface visual do dashboard
├── 📄 Graficos.gs           # Criação de gráficos interativos
├── 📄 AbaMetricas.gs        # Aba detalhada de métricas
├── 📄 Configuracoes.gs      # Configurações e funções auxiliares
└── 📄 README.md             # Este arquivo de instruções
```

## 🚀 Instalação e Configuração

### Passo 1: Preparar o Arquivo de Dados

1. **Formato do arquivo `data.txt`**:
   ```
   ID|Título|Responsável|Status|Data_Início|Data_Fim|Orçamento|Progresso|Categoria|Avaliador
   APQ-1386-21|PROJETO EXEMPLO|João Silva|Em Andamento|2024-01-15|2025-12-01|150000|75|Tecnologia|Maria Santos
   ```

2. **Fazer upload para o Google Drive**:
   - Acesse [drive.google.com](https://drive.google.com)
   - Faça upload do arquivo `data.txt`
   - Certifique-se de que o nome está correto

### Passo 2: Criar a Planilha Google Sheets

1. **Criar nova planilha**:
   - Acesse [sheets.google.com](https://sheets.google.com)
   - Clique em "Planilha em branco"
   - Renomeie para "Dashboard Projetos FACEPE"

2. **Abrir o Apps Script Editor**:
   - Na planilha, vá em `Extensões` → `Apps Script`
   - Remova o código padrão do arquivo `Code.gs`

### Passo 3: Instalar o Código

1. **Copiar arquivos .gs**:
   - Cole o conteúdo de cada arquivo `.gs` no Apps Script Editor
   - Mantenha os nomes dos arquivos conforme especificado

2. **Salvar o projeto**:
   - Clique em `Ctrl+S` ou no ícone de salvar
   - Nomeie o projeto como "Dashboard Projetos"

### Passo 4: Configurar Permissões

1. **Executar função inicial**:
   - Selecione a função `inicializarDashboard`
   - Clique em "Executar"
   - Autorize as permissões solicitadas:
     - ✅ Acessar Google Sheets
     - ✅ Acessar Google Drive
     - ✅ Enviar emails
     - ✅ Criar triggers

### Passo 5: Primeira Execução

1. **Executar inicialização**:
   ```javascript
   inicializarDashboard()
   ```

2. **Verificar criação das abas**:
   - 📊 Dashboard
   - 📋 Métricas
   - 📈 Gráficos
   - 📁 Dados_Projetos

## 🎛️ Como Usar

### Menu Principal

Após a instalação, você terá um menu personalizado:

```
📊 Dashboard Projetos
├── 🔄 Atualizar Dados
├── 📈 Recalcular Métricas
├── 📊 Recriar Gráficos
├── ─────────────────
├── 📁 Reimportar Arquivo
├── 📧 Enviar Relatório
├── ─────────────────
├── ⚙️ Configurações
└── ❓ Ajuda
```

### Funcionalidades Principais

#### 1. **Dashboard Principal** (Aba Dashboard)
- 📈 Resumo geral de projetos
- 🎯 Distribuição por status
- 💰 Análise de orçamento
- 👥 Performance dos avaliadores
- ⚠️ Projetos com prazos críticos

#### 2. **Métricas Detalhadas** (Aba Métricas)
- 📊 KPIs calculados automaticamente
- 📈 Tendências temporais
- 📂 Distribuição por categoria
- 👥 Performance individual dos avaliadores

#### 3. **Gráficos Interativos** (Aba Gráficos)
- 🥧 Gráfico de pizza (status)
- 📊 Gráfico de barras (categorias)
- 📈 Gráfico de linha (tendências)
- 💰 Gráfico de orçamento

### Atualização de Dados

#### Automática
- **Diária**: 8h da manhã (padrão)
- **Semanal**: Segundas-feiras às 8h
- **Mensal**: Dia 1 de cada mês às 8h

#### Manual
- Use o menu `📊 Dashboard Projetos` → `🔄 Atualizar Dados`
- Ou execute a função `atualizarDashboard()`

## ⚙️ Configurações Avançadas

### Painel de Configurações

Acesse via menu: `📊 Dashboard Projetos` → `⚙️ Configurações`

#### Opções Disponíveis:

1. **📁 Arquivo de Dados**
   - Nome do arquivo no Google Drive
   - Verificação de existência

2. **🎨 Cores dos Status**
   - Personalizar cores para cada status
   - Aplicação automática em gráficos

3. **⏰ Atualização Automática**
   - Frequência: Diária/Semanal/Mensal/Manual
   - Horário configurável

4. **📧 Relatórios por Email**
   - Email de destino
   - Frequência de envio

5. **🚨 Alertas de Prazo**
   - Dias para alerta crítico
   - Limite de progresso para alerta

## 📊 Formato dos Dados

### Estrutura do arquivo data.txt

| Campo | Tipo | Descrição | Exemplo |
|-------|------|-----------|---------|
| ID | Texto | Identificador único | APQ-1386-21 |
| Título | Texto | Nome do projeto | DESENVOLVIMENTO TECNOLÓGICO |
| Responsável | Texto | Nome do responsável | João Silva |
| Status | Texto | Status atual | Em Andamento |
| Data_Início | Data | Data de início | 2024-01-15 |
| Data_Fim | Data | Data prevista de fim | 2025-12-01 |
| Orçamento | Número | Valor em reais | 150000 |
| Progresso | Número | Percentual (0-100) | 75 |
| Categoria | Texto | Categoria do projeto | Tecnologia |
| Avaliador | Texto | Nome do avaliador | Maria Santos |

### Status Válidos
- ✅ **Concluído**: Projeto finalizado
- 🔄 **Em Andamento**: Projeto em execução
- ⚠️ **Atrasado**: Projeto com atraso
- ❌ **Cancelado**: Projeto cancelado
- ⏸️ **Pausado**: Projeto temporariamente pausado

## 🔧 Manutenção e Troubleshooting

### Problemas Comuns

#### 1. **Arquivo não encontrado**
```
❌ Erro: Arquivo data.txt não encontrado
```
**Solução**: Verifique se o arquivo está no Google Drive com o nome correto.

#### 2. **Dados não carregam**
```
❌ Erro: Nenhum dado encontrado
```
**Solução**: Verifique o formato do arquivo (separador pipe `|`).

#### 3. **Gráficos não aparecem**
```
❌ Gráficos em branco
```
**Solução**: Execute `📊 Dashboard Projetos` → `📊 Recriar Gráficos`.

#### 4. **Triggers não funcionam**
```
❌ Atualização automática falhou
```
**Solução**: Reconfigure em `⚙️ Configurações` → `⏰ Atualização Automática`.

### Logs e Debugging

1. **Visualizar logs**:
   - Apps Script Editor → `Execuções`
   - Verificar erros e tempos de execução

2. **Console de debug**:
   ```javascript
   console.log('Debug info:', variavel);
   ```

### Backup e Restauração

#### Criar Backup Manual
```javascript
criarBackup()
```

#### Localização dos Backups
- Pasta: `Dashboard_Backups` no Google Drive
- Formato: `Backup_Dashboard_YYYY-MM-DD`

## 📈 Métricas Calculadas

### KPIs Principais
- **Total de Projetos**: Contagem total
- **Orçamento Total**: Soma de todos os orçamentos
- **Progresso Médio**: Média ponderada do progresso
- **Taxa de Conclusão**: % de projetos concluídos
- **Projetos Críticos**: Com prazo < 30 dias

### Análises Avançadas
- **Distribuição por Status**: Percentuais por categoria
- **Performance por Avaliador**: Métricas individuais
- **Tendências Temporais**: Evolução mensal
- **Análise de Orçamento**: Estatísticas financeiras

## 🚀 Funcionalidades Futuras

### Roadmap
- [ ] Integração com Google Calendar
- [ ] Notificações push
- [ ] Dashboard mobile responsivo
- [ ] Exportação para PDF
- [ ] Integração com APIs externas
- [ ] Análise preditiva com ML

## 📞 Suporte

### Documentação
- **Google Apps Script**: [developers.google.com/apps-script](https://developers.google.com/apps-script)
- **Google Sheets API**: [developers.google.com/sheets](https://developers.google.com/sheets)

### Contato
- **Desenvolvedor**: Sistema FACEPE
- **Versão**: 1.0.0
- **Última atualização**: 2025

---

**🎉 Dashboard pronto para uso! Aproveite o acompanhamento inteligente dos seus projetos!**
