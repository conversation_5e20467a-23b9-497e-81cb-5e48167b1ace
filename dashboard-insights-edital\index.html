<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard FACEPE - Pernambucanas Inovadoras</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header__content">
                <div class="header__brand">
                    <h1 class="header__title">FACEPE Dashboard</h1>
                    <span class="header__subtitle">Pernambucanas Inovadoras 2025</span>
                </div>
                <div class="header__actions">
                    <button class="btn btn--secondary theme-toggle" id="themeToggle">
                        <span class="theme-icon">🌙</span>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Layout -->
    <div class="main-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar__content">
                <h3 class="sidebar__title">Filtros</h3>
                
                <div class="filter-group">
                    <label class="form-label">Modalidade</label>
                    <select class="form-control" id="modalidadeFilter">
                        <option value="">Todas as modalidades</option>
                        <option value="Parceria">Parceria</option>
                        <option value="Novas Spin-Offs Acadêmicas">Novas Spin-Offs</option>
                        <option value="Jovem Empreendedora">Jovem Empreendedora</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="form-label">Região</label>
                    <select class="form-control" id="regiaoFilter">
                        <option value="">Todas as regiões</option>
                        <option value="RD 12 - REGIÃO METROPOLITANA">Região Metropolitana</option>
                        <option value="RD 02 - SERTÃO DO SÃO FRANCISCO">Sertão do São Francisco</option>
                        <option value="RD 10 - MATA SUL">Mata Sul</option>
                        <option value="RD 08 - AGRESTE CENTRAL">Agreste Central</option>
                        <option value="RD 09 - AGRESTE SETENTRIONAL">Agreste Setentrional</option>
                        <option value="RD 07 - AGRESTE MERIDIONAL">Agreste Meridional</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="form-label">Pesquisar</label>
                    <input type="text" class="form-control" id="searchInput" placeholder="Buscar projetos...">
                </div>

                <button class="btn btn--secondary btn--full-width" id="clearFilters">
                    Limpar Filtros
                </button>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="content">
            <!-- Navigation Tabs -->
            <nav class="tabs">
                <button class="tab-btn active" data-tab="dashboard">Dashboard</button>
                <button class="tab-btn" data-tab="projetos">Projetos</button>
                <button class="tab-btn" data-tab="analytics">Analytics</button>
            </nav>

            <!-- Dashboard Tab -->
            <div class="tab-content active" id="dashboard">
                <!-- KPI Cards -->
                <div class="kpi-grid">
                    <div class="kpi-card">
                        <div class="kpi-card__header">
                            <h3>Total de Projetos</h3>
                            <span class="kpi-icon">📊</span>
                        </div>
                        <div class="kpi-card__value">63</div>
                        <div class="kpi-card__subtitle">Projetos aprovados</div>
                    </div>

                    <div class="kpi-card">
                        <div class="kpi-card__header">
                            <h3>Instituições</h3>
                            <span class="kpi-icon">🏛️</span>
                        </div>
                        <div class="kpi-card__value">12</div>
                        <div class="kpi-card__subtitle">Participantes</div>
                    </div>

                    <div class="kpi-card">
                        <div class="kpi-card__header">
                            <h3>Regiões</h3>
                            <span class="kpi-icon">📍</span>
                        </div>
                        <div class="kpi-card__value">6</div>
                        <div class="kpi-card__subtitle">De desenvolvimento</div>
                    </div>

                    <div class="kpi-card">
                        <div class="kpi-card__header">
                            <h3>ODS</h3>
                            <span class="kpi-icon">🎯</span>
                        </div>
                        <div class="kpi-card__value">13</div>
                        <div class="kpi-card__subtitle">Contemplados</div>
                    </div>
                </div>

                <!-- Charts Grid -->
                <div class="charts-grid">
                    <div class="chart-container">
                        <div class="chart-header">
                            <h3>Distribuição por Modalidade</h3>
                        </div>
                        <div class="chart-wrapper">
                            <canvas id="modalidadeChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-container">
                        <div class="chart-header">
                            <h3>Distribuição Regional</h3>
                        </div>
                        <div class="chart-wrapper">
                            <canvas id="regionalChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-container chart-container--large">
                        <div class="chart-header">
                            <h3>Projetos por ODS</h3>
                        </div>
                        <div class="chart-wrapper">
                            <canvas id="odsChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-container chart-container--large">
                        <div class="chart-header">
                            <h3>Participação Institucional</h3>
                        </div>
                        <div class="chart-wrapper">
                            <canvas id="instituicaoChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Projetos Tab -->
            <div class="tab-content" id="projetos">
                <div class="projects-header">
                    <h2>Lista de Projetos</h2>
                    <div class="projects-actions">
                        <button class="btn btn--secondary" id="exportBtn">
                            📊 Exportar Relatório
                        </button>
                    </div>
                </div>

                <div class="projects-grid" id="projectsGrid">
                    <!-- Projects will be dynamically loaded here -->
                </div>

                <div class="pagination" id="pagination">
                    <!-- Pagination will be dynamically loaded here -->
                </div>
            </div>

            <!-- Analytics Tab -->
            <div class="tab-content" id="analytics">
                <div class="analytics-grid">
                    <div class="analytics-card">
                        <h3>Resumo Executivo</h3>
                        <div class="metric-row">
                            <span class="metric-label">Taxa de Aprovação:</span>
                            <span class="metric-value">100%</span>
                        </div>
                        <div class="metric-row">
                            <span class="metric-label">Modalidade Líder:</span>
                            <span class="metric-value">Parceria (49,2%)</span>
                        </div>
                        <div class="metric-row">
                            <span class="metric-label">ODS Principal:</span>
                            <span class="metric-value">Saúde e Bem-estar</span>
                        </div>
                        <div class="metric-row">
                            <span class="metric-label">Instituição Líder:</span>
                            <span class="metric-value">UFPE (39,7%)</span>
                        </div>
                    </div>

                    <div class="analytics-card">
                        <h3>Distribuição Geográfica</h3>
                        <div class="progress-item">
                            <div class="progress-header">
                                <span>Região Metropolitana</span>
                                <span>74,6%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 74.6%"></div>
                            </div>
                        </div>
                        <div class="progress-item">
                            <div class="progress-header">
                                <span>Sertão do São Francisco</span>
                                <span>7,9%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 7.9%"></div>
                            </div>
                        </div>
                        <div class="progress-item">
                            <div class="progress-header">
                                <span>Mata Sul</span>
                                <span>7,9%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 7.9%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Modal -->
    <div class="modal" id="projectModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">Detalhes do Projeto</h3>
                <button class="modal-close" id="modalClose">&times;</button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- Project details will be loaded here -->
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>