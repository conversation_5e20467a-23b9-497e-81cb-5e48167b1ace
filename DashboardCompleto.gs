/**
 * ========================================
 * DASHBOARD DE PROJETOS FACEPE - WEB APP COMPLETA
 * Sistema integrado em Google Apps Script
 * Versão: 1.0.0 | Data: 2025
 * ========================================
 */

// ==================== FUNÇÕES WEB APP ====================
/**
 * Função principal para servir a aplicação web
 */
function doGet(e) {
  try {
    console.log('🌐 Iniciando Web App...');

    // Verificar se dados existem, senão importar
    verificarEInicializarDados();

    // Criar template HTML
    const template = HtmlService.createTemplateFromFile('index');

    // Passar dados para o template
    template.dadosDashboard = obterDadosDashboard();
    template.metricas = obterMetricasCompletas();
    template.graficos = obterDadosGraficos();

    // Retornar página HTML
    return template.evaluate()
      .setTitle('📊 Dashboard de Projetos FACEPE')
      .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL)
      .addMetaTag('viewport', 'width=device-width, initial-scale=1');

  } catch (error) {
    console.error('❌ Erro na Web App:', error);

    // Retornar página de erro
    const errorTemplate = HtmlService.createTemplate(`
      <html>
        <head>
          <title>Erro - Dashboard FACEPE</title>
          <style>
            body { font-family: Arial, sans-serif; padding: 20px; text-align: center; }
            .error { color: #d32f2f; background: #ffebee; padding: 20px; border-radius: 8px; }
          </style>
        </head>
        <body>
          <div class="error">
            <h2>❌ Erro na Aplicação</h2>
            <p><strong>Erro:</strong> <?= error ?></p>
            <p>Verifique se o arquivo data.txt está no Google Drive e tente novamente.</p>
            <button onclick="location.reload()">🔄 Tentar Novamente</button>
          </div>
        </body>
      </html>
    `);

    errorTemplate.error = error.message;
    return errorTemplate.evaluate();
  }
}

/**
 * Função para incluir arquivos HTML/CSS/JS
 */
function include(filename) {
  return HtmlService.createHtmlOutputFromFile(filename).getContent();
}

/**
 * Verifica e inicializa dados se necessário
 */
function verificarEInicializarDados() {
  const planilha = SpreadsheetApp.getActiveSpreadsheet();
  let abaDados = planilha.getSheetByName(CONFIG.ABA_DADOS);

  // Se não existe aba de dados, inicializar
  if (!abaDados || abaDados.getLastRow() <= 1) {
    console.log('📋 Inicializando dados...');
    inicializarDashboard();
  }
}

/**
 * Obtém dados do dashboard para a web app
 */
function obterDadosDashboard() {
  try {
    const planilha = SpreadsheetApp.getActiveSpreadsheet();
    const abaDados = planilha.getSheetByName(CONFIG.ABA_DADOS);

    if (!abaDados) return [];

    const dados = abaDados.getDataRange().getValues();

    if (dados.length <= 1) return [];

    // Converter para formato JSON
    const cabecalho = dados[0];
    const projetos = [];

    for (let i = 1; i < dados.length; i++) {
      const projeto = {};
      cabecalho.forEach((col, index) => {
        projeto[col] = dados[i][index];
      });
      projetos.push(projeto);
    }

    return projetos;

  } catch (error) {
    console.error('Erro ao obter dados:', error);
    return [];
  }
}

/**
 * Obtém métricas completas para a web app
 */
function obterMetricasCompletas() {
  try {
    const planilha = SpreadsheetApp.getActiveSpreadsheet();
    const abaDados = planilha.getSheetByName(CONFIG.ABA_DADOS);

    if (!abaDados) return {};

    const dados = obterDadosProjetos(abaDados);

    if (dados.length === 0) return {};

    return {
      resumoGeral: calcularResumoGeral(dados),
      statusDistribuicao: calcularDistribuicaoStatus(dados),
      orcamentoAnalise: calcularAnaliseOrcamento(dados),
      avaliadoresPerformance: calcularPerformanceAvaliadores(dados),
      categoriaDistribuicao: calcularDistribuicaoCategoria(dados),
      prazosCriticos: identificarPrazosCriticos(dados),
      tendencias: calcularTendencias(dados)
    };

  } catch (error) {
    console.error('Erro ao calcular métricas:', error);
    return {};
  }
}

/**
 * Obtém dados para gráficos
 */
function obterDadosGraficos() {
  try {
    const planilha = SpreadsheetApp.getActiveSpreadsheet();
    const abaDados = planilha.getSheetByName(CONFIG.ABA_DADOS);

    if (!abaDados) return {};

    const dados = abaDados.getDataRange().getValues();

    if (dados.length <= 1) return {};

    // Dados para gráfico de status
    const statusCount = {};
    for (let i = 1; i < dados.length; i++) {
      const status = dados[i][3] || 'Não Definido';
      statusCount[status] = (statusCount[status] || 0) + 1;
    }

    // Dados para gráfico de categorias
    const categoriaCount = {};
    for (let i = 1; i < dados.length; i++) {
      const categoria = dados[i][8] || 'Não Definido';
      categoriaCount[categoria] = (categoriaCount[categoria] || 0) + 1;
    }

    // Dados para gráfico de orçamento
    const orcamentoCategoria = {};
    for (let i = 1; i < dados.length; i++) {
      const categoria = dados[i][8] || 'Não Definido';
      const orcamento = parseFloat(dados[i][6]) || 0;
      orcamentoCategoria[categoria] = (orcamentoCategoria[categoria] || 0) + orcamento;
    }

    return {
      status: statusCount,
      categorias: categoriaCount,
      orcamento: orcamentoCategoria,
      cores: CONFIG.CORES
    };

  } catch (error) {
    console.error('Erro ao obter dados de gráficos:', error);
    return {};
  }
}

// ==================== CONFIGURAÇÕES GLOBAIS ====================
const CONFIG = {
  // Nomes das abas
  ABA_DADOS: 'Dados_Projetos',
  ABA_DASHBOARD: 'Dashboard',
  ABA_METRICAS: 'Métricas',
  ABA_GRAFICOS: 'Gráficos',
  
  // Cores para status
  CORES: {
    'Concluído': '#4CAF50',
    'Em Andamento': '#FF9800', 
    'Atrasado': '#F44336',
    'Cancelado': '#9E9E9E',
    'Pausado': '#9C27B0'
  },
  
  // Configurações de formatação
  FONTE_TITULO: 'Roboto',
  TAMANHO_TITULO: 14,
  TAMANHO_TEXTO: 11
};

// ==================== FUNÇÃO PRINCIPAL ====================
/**
 * Função principal que inicializa todo o dashboard
 * Execute esta função para configurar tudo
 */
function inicializarDashboard() {
  try {
    console.log('🚀 Iniciando configuração do dashboard...');
    
    // 1. Importar dados do arquivo
    importarDadosDoArquivo();
    
    // 2. Criar estrutura das abas
    criarEstruturaPlanilha();
    
    // 3. Processar e calcular métricas
    calcularMetricas();
    
    // 4. Criar dashboard visual
    criarDashboardVisual();
    
    // 5. Configurar menu personalizado
    criarMenuCustomizado();
    
    // 6. Configurar triggers automáticos
    configurarTriggers();
    
    SpreadsheetApp.getUi().alert('✅ Dashboard configurado com sucesso!\n\nVerifique as abas criadas e o menu "📊 Dashboard Projetos"');
    
  } catch (error) {
    console.error('❌ Erro na inicialização:', error);
    SpreadsheetApp.getUi().alert('❌ Erro: ' + error.message);
  }
}

// ==================== IMPORTAÇÃO DE DADOS ====================
/**
 * Importa dados do arquivo data.txt
 */
function importarDadosDoArquivo() {
  try {
    console.log('📁 Importando dados do arquivo...');
    
    // Procurar arquivo data.txt no Drive
    const arquivos = DriveApp.getFilesByName('data.txt');
    
    if (!arquivos.hasNext()) {
      throw new Error('Arquivo data.txt não encontrado no Google Drive.\n\nPor favor:\n1. Faça upload do arquivo data.txt para o Google Drive\n2. Execute novamente esta função');
    }
    
    const arquivo = arquivos.next();
    const conteudo = arquivo.getBlob().getDataAsString();
    
    // Processar dados do arquivo
    const linhas = conteudo.split('\n');
    const cabecalho = linhas[0].split('|');
    
    // Preparar dados para a planilha
    const dados = [cabecalho];
    
    for (let i = 1; i < linhas.length; i++) {
      if (linhas[i].trim()) {
        dados.push(linhas[i].split('|'));
      }
    }
    
    // Inserir dados na planilha
    const planilha = SpreadsheetApp.getActiveSpreadsheet();
    let aba = planilha.getSheetByName(CONFIG.ABA_DADOS);
    
    if (!aba) {
      aba = planilha.insertSheet(CONFIG.ABA_DADOS);
    }
    
    // Limpar dados existentes
    aba.clear();
    
    // Inserir novos dados
    if (dados.length > 0) {
      aba.getRange(1, 1, dados.length, dados[0].length).setValues(dados);
      
      // Formatar cabeçalho
      formatarCabecalho(aba, dados[0].length);
      
      // Aplicar formatação condicional
      aplicarFormatacaoCondicional(aba, dados.length);
    }
    
    console.log(`✅ Importados ${dados.length - 1} projetos com sucesso`);
    
  } catch (error) {
    console.error('❌ Erro na importação:', error);
    throw error;
  }
}

/**
 * Formata o cabeçalho da tabela de dados
 */
function formatarCabecalho(aba, numColunas) {
  const cabecalho = aba.getRange(1, 1, 1, numColunas);
  
  cabecalho
    .setBackground('#1f4e79')
    .setFontColor('#ffffff')
    .setFontWeight('bold')
    .setFontSize(CONFIG.TAMANHO_TITULO)
    .setHorizontalAlignment('center')
    .setVerticalAlignment('middle');
    
  // Ajustar largura das colunas
  aba.autoResizeColumns(1, numColunas);
  
  // Congelar primeira linha
  aba.setFrozenRows(1);
}

/**
 * Aplica formatação condicional baseada no status
 */
function aplicarFormatacaoCondicional(aba, numLinhas) {
  if (numLinhas <= 1) return;
  
  // Encontrar coluna de status (assumindo que é a 4ª coluna)
  const colunaStatus = 4;
  const rangeStatus = aba.getRange(2, colunaStatus, numLinhas - 1, 1);
  
  // Limpar regras existentes
  aba.clearConditionalFormatRules();
  
  const regras = [];
  
  // Criar regras para cada status
  Object.keys(CONFIG.CORES).forEach(status => {
    const regra = SpreadsheetApp.newConditionalFormatRule()
      .whenTextEqualTo(status)
      .setBackground(CONFIG.CORES[status])
      .setFontColor('#ffffff')
      .setRanges([rangeStatus])
      .build();
    regras.push(regra);
  });
  
  aba.setConditionalFormatRules(regras);
}

// ==================== CÁLCULO DE MÉTRICAS ====================
/**
 * Calcula todas as métricas e atualiza a aba de métricas
 */
function calcularMetricas() {
  try {
    console.log('📊 Calculando métricas...');
    
    const planilha = SpreadsheetApp.getActiveSpreadsheet();
    const abaDados = planilha.getSheetByName(CONFIG.ABA_DADOS);
    
    if (!abaDados) {
      throw new Error('Aba de dados não encontrada. Execute primeiro a importação.');
    }
    
    // Obter dados
    const dados = obterDadosProjetos(abaDados);
    
    if (dados.length === 0) {
      throw new Error('Nenhum dado encontrado para calcular métricas.');
    }
    
    // Calcular métricas
    const metricas = {
      resumoGeral: calcularResumoGeral(dados),
      statusDistribuicao: calcularDistribuicaoStatus(dados),
      orcamentoAnalise: calcularAnaliseOrcamento(dados),
      progressoMedio: calcularProgressoMedio(dados),
      avaliadoresPerformance: calcularPerformanceAvaliadores(dados),
      categoriaDistribuicao: calcularDistribuicaoCategoria(dados),
      prazosCriticos: identificarPrazosCriticos(dados),
      tendencias: calcularTendencias(dados)
    };
    
    // Criar/atualizar aba de métricas
    criarAbaMetricas(planilha, metricas);
    
    console.log('✅ Métricas calculadas com sucesso');
    
  } catch (error) {
    console.error('❌ Erro no cálculo de métricas:', error);
    throw error;
  }
}

/**
 * Obtém dados estruturados da aba de projetos
 */
function obterDadosProjetos(aba) {
  const dados = aba.getDataRange().getValues();
  
  if (dados.length <= 1) return [];
  
  const cabecalho = dados[0];
  const projetos = [];
  
  // Mapear índices das colunas
  const indices = {
    id: cabecalho.indexOf('ID'),
    titulo: cabecalho.indexOf('Título'),
    responsavel: cabecalho.indexOf('Responsável'),
    status: cabecalho.indexOf('Status'),
    dataInicio: cabecalho.indexOf('Data_Início'),
    dataFim: cabecalho.indexOf('Data_Fim'),
    orcamento: cabecalho.indexOf('Orçamento'),
    progresso: cabecalho.indexOf('Progresso'),
    categoria: cabecalho.indexOf('Categoria'),
    avaliador: cabecalho.indexOf('Avaliador')
  };
  
  // Processar cada linha de dados
  for (let i = 1; i < dados.length; i++) {
    const linha = dados[i];
    
    const projeto = {
      id: linha[indices.id] || '',
      titulo: linha[indices.titulo] || '',
      responsavel: linha[indices.responsavel] || '',
      status: linha[indices.status] || '',
      dataInicio: parseData(linha[indices.dataInicio]),
      dataFim: parseData(linha[indices.dataFim]),
      orcamento: parseFloat(linha[indices.orcamento]) || 0,
      progresso: parseFloat(linha[indices.progresso]) || 0,
      categoria: linha[indices.categoria] || '',
      avaliador: linha[indices.avaliador] || ''
    };
    
    projetos.push(projeto);
  }
  
  return projetos;
}

/**
 * Converte string de data para objeto Date
 */
function parseData(dataStr) {
  if (!dataStr) return null;
  
  try {
    // Formato esperado: YYYY-MM-DD
    if (typeof dataStr === 'string') {
      const partes = dataStr.split('-');
      if (partes.length === 3) {
        return new Date(parseInt(partes[0]), parseInt(partes[1]) - 1, parseInt(partes[2]));
      }
    }
    
    // Se já é uma data
    if (dataStr instanceof Date) {
      return dataStr;
    }
    
    return new Date(dataStr);
  } catch (error) {
    return null;
  }
}

/**
 * Calcula resumo geral dos projetos
 */
function calcularResumoGeral(projetos) {
  const total = projetos.length;
  const orcamentoTotal = projetos.reduce((sum, p) => sum + p.orcamento, 0);
  const progressoMedio = projetos.reduce((sum, p) => sum + p.progresso, 0) / total;
  
  const statusCount = {};
  projetos.forEach(p => {
    statusCount[p.status] = (statusCount[p.status] || 0) + 1;
  });
  
  return {
    totalProjetos: total,
    orcamentoTotal: orcamentoTotal,
    progressoMedio: Math.round(progressoMedio * 100) / 100,
    statusCount: statusCount
  };
}

/**
 * Calcula distribuição por status
 */
function calcularDistribuicaoStatus(projetos) {
  const distribuicao = {};
  const total = projetos.length;
  
  projetos.forEach(projeto => {
    const status = projeto.status || 'Não Definido';
    distribuicao[status] = (distribuicao[status] || 0) + 1;
  });
  
  // Converter para percentuais
  Object.keys(distribuicao).forEach(status => {
    distribuicao[status] = {
      quantidade: distribuicao[status],
      percentual: Math.round((distribuicao[status] / total) * 100 * 100) / 100
    };
  });
  
  return distribuicao;
}

/**
 * Calcula análise de orçamento
 */
function calcularAnaliseOrcamento(projetos) {
  const orcamentos = projetos.map(p => p.orcamento).filter(o => o > 0);

  if (orcamentos.length === 0) {
    return { total: 0, medio: 0, maximo: 0, minimo: 0 };
  }

  const total = orcamentos.reduce((sum, o) => sum + o, 0);
  const medio = total / orcamentos.length;
  const maximo = Math.max(...orcamentos);
  const minimo = Math.min(...orcamentos);

  return {
    total: total,
    medio: Math.round(medio),
    maximo: maximo,
    minimo: minimo
  };
}

/**
 * Calcula progresso médio por categoria
 */
function calcularProgressoMedio(projetos) {
  const progressoPorCategoria = {};

  projetos.forEach(projeto => {
    const categoria = projeto.categoria || 'Não Definido';

    if (!progressoPorCategoria[categoria]) {
      progressoPorCategoria[categoria] = {
        total: 0,
        count: 0,
        projetos: []
      };
    }

    progressoPorCategoria[categoria].total += projeto.progresso;
    progressoPorCategoria[categoria].count += 1;
    progressoPorCategoria[categoria].projetos.push(projeto.id);
  });

  // Calcular médias
  Object.keys(progressoPorCategoria).forEach(categoria => {
    const dados = progressoPorCategoria[categoria];
    dados.media = Math.round((dados.total / dados.count) * 100) / 100;
  });

  return progressoPorCategoria;
}

/**
 * Calcula performance dos avaliadores
 */
function calcularPerformanceAvaliadores(projetos) {
  const performance = {};

  projetos.forEach(projeto => {
    const avaliador = projeto.avaliador || 'Não Atribuído';

    if (!performance[avaliador]) {
      performance[avaliador] = {
        totalProjetos: 0,
        concluidos: 0,
        emAndamento: 0,
        atrasados: 0,
        orcamentoTotal: 0,
        progressoMedio: 0
      };
    }

    const perf = performance[avaliador];
    perf.totalProjetos += 1;
    perf.orcamentoTotal += projeto.orcamento;
    perf.progressoMedio += projeto.progresso;

    switch (projeto.status) {
      case 'Concluído':
        perf.concluidos += 1;
        break;
      case 'Em Andamento':
        perf.emAndamento += 1;
        break;
      case 'Atrasado':
        perf.atrasados += 1;
        break;
    }
  });

  // Calcular médias finais
  Object.keys(performance).forEach(avaliador => {
    const perf = performance[avaliador];
    perf.progressoMedio = Math.round((perf.progressoMedio / perf.totalProjetos) * 100) / 100;
    perf.taxaConclusao = Math.round((perf.concluidos / perf.totalProjetos) * 100 * 100) / 100;
  });

  return performance;
}

/**
 * Calcula distribuição por categoria
 */
function calcularDistribuicaoCategoria(projetos) {
  const distribuicao = {};

  projetos.forEach(projeto => {
    const categoria = projeto.categoria || 'Não Definido';

    if (!distribuicao[categoria]) {
      distribuicao[categoria] = {
        quantidade: 0,
        orcamentoTotal: 0,
        progressoMedio: 0
      };
    }

    distribuicao[categoria].quantidade += 1;
    distribuicao[categoria].orcamentoTotal += projeto.orcamento;
    distribuicao[categoria].progressoMedio += projeto.progresso;
  });

  // Calcular médias
  Object.keys(distribuicao).forEach(categoria => {
    const dados = distribuicao[categoria];
    dados.progressoMedio = Math.round((dados.progressoMedio / dados.quantidade) * 100) / 100;
  });

  return distribuicao;
}

/**
 * Identifica projetos com prazos críticos
 */
function identificarPrazosCriticos(projetos) {
  const hoje = new Date();
  const prazosCriticos = [];

  projetos.forEach(projeto => {
    if (projeto.dataFim && projeto.status !== 'Concluído') {
      const diasRestantes = Math.ceil((projeto.dataFim - hoje) / (1000 * 60 * 60 * 24));

      if (diasRestantes <= 30) {
        prazosCriticos.push({
          id: projeto.id,
          titulo: projeto.titulo,
          responsavel: projeto.responsavel,
          dataFim: projeto.dataFim,
          diasRestantes: diasRestantes,
          progresso: projeto.progresso,
          status: projeto.status
        });
      }
    }
  });

  // Ordenar por dias restantes
  prazosCriticos.sort((a, b) => a.diasRestantes - b.diasRestantes);

  return prazosCriticos;
}

/**
 * Calcula tendências temporais
 */
function calcularTendencias(projetos) {
  const anoAtual = new Date().getFullYear();
  const tendencias = {
    projetosPorMes: {},
    orcamentoPorMes: {},
    conclusoesPorMes: {}
  };

  projetos.forEach(projeto => {
    if (projeto.dataInicio) {
      const ano = projeto.dataInicio.getFullYear();
      const mes = projeto.dataInicio.getMonth() + 1;
      const chave = `${ano}-${mes.toString().padStart(2, '0')}`;

      // Projetos iniciados por mês
      tendencias.projetosPorMes[chave] = (tendencias.projetosPorMes[chave] || 0) + 1;

      // Orçamento por mês
      tendencias.orcamentoPorMes[chave] = (tendencias.orcamentoPorMes[chave] || 0) + projeto.orcamento;
    }

    // Conclusões por mês
    if (projeto.status === 'Concluído' && projeto.dataFim) {
      const ano = projeto.dataFim.getFullYear();
      const mes = projeto.dataFim.getMonth() + 1;
      const chave = `${ano}-${mes.toString().padStart(2, '0')}`;

      tendencias.conclusoesPorMes[chave] = (tendencias.conclusoesPorMes[chave] || 0) + 1;
    }
  });

  return tendencias;
}

// ==================== CRIAÇÃO DO DASHBOARD VISUAL ====================
/**
 * Cria o dashboard visual principal
 */
function criarDashboardVisual() {
  try {
    console.log('🎨 Criando dashboard visual...');

    const planilha = SpreadsheetApp.getActiveSpreadsheet();

    // Criar/obter aba do dashboard
    let abaDashboard = planilha.getSheetByName(CONFIG.ABA_DASHBOARD);
    if (!abaDashboard) {
      abaDashboard = planilha.insertSheet(CONFIG.ABA_DASHBOARD);
    }

    // Limpar conteúdo existente
    abaDashboard.clear();

    // Configurar layout do dashboard
    configurarLayoutDashboard(abaDashboard);

    // Criar seções do dashboard
    criarSecaoResumo(abaDashboard);
    criarSecaoStatus(abaDashboard);
    criarSecaoOrcamento(abaDashboard);
    criarSecaoAvaliadores(abaDashboard);
    criarSecaoPrazosCriticos(abaDashboard);

    // Criar gráficos
    criarGraficos();

    // Aplicar formatação final
    aplicarFormatacaoFinal(abaDashboard);

    console.log('✅ Dashboard visual criado com sucesso');

  } catch (error) {
    console.error('❌ Erro na criação do dashboard:', error);
    throw error;
  }
}

/**
 * Configura o layout básico do dashboard
 */
function configurarLayoutDashboard(aba) {
  // Definir larguras das colunas
  aba.setColumnWidth(1, 200);  // Coluna A - Labels
  aba.setColumnWidth(2, 150);  // Coluna B - Valores
  aba.setColumnWidth(3, 50);   // Coluna C - Espaçador
  aba.setColumnWidth(4, 200);  // Coluna D - Labels
  aba.setColumnWidth(5, 150);  // Coluna E - Valores
  aba.setColumnWidth(6, 50);   // Coluna F - Espaçador
  aba.setColumnWidth(7, 300);  // Coluna G - Gráficos
  aba.setColumnWidth(8, 300);  // Coluna H - Gráficos

  // Título principal
  aba.getRange('A1:H1').merge();
  aba.getRange('A1').setValue('📊 DASHBOARD DE PROJETOS FACEPE');
  aba.getRange('A1')
    .setFontSize(20)
    .setFontWeight('bold')
    .setHorizontalAlignment('center')
    .setBackground('#1f4e79')
    .setFontColor('#ffffff');

  // Data de atualização
  aba.getRange('A2:H2').merge();
  aba.getRange('A2').setValue(`Última atualização: ${new Date().toLocaleString('pt-BR')}`);
  aba.getRange('A2')
    .setFontSize(10)
    .setHorizontalAlignment('center')
    .setBackground('#f0f0f0');
}

/**
 * Cria seção de resumo geral
 */
function criarSecaoResumo(aba) {
  const planilha = SpreadsheetApp.getActiveSpreadsheet();
  const abaMetricas = planilha.getSheetByName(CONFIG.ABA_METRICAS);

  if (!abaMetricas) return;

  // Título da seção
  aba.getRange('A4').setValue('📈 RESUMO GERAL');
  aba.getRange('A4:B4')
    .setFontWeight('bold')
    .setFontSize(14)
    .setBackground('#e6f3ff');

  // Buscar dados das métricas
  const dadosMetricas = abaMetricas.getDataRange().getValues();
  let totalProjetos = 0, orcamentoTotal = 0, progressoMedio = 0;

  // Encontrar valores nas métricas
  for (let i = 0; i < dadosMetricas.length; i++) {
    const linha = dadosMetricas[i];
    if (linha[0] === 'Total de Projetos') totalProjetos = linha[1];
    if (linha[0] === 'Orçamento Total') orcamentoTotal = linha[1];
    if (linha[0] === 'Progresso Médio') progressoMedio = linha[1];
  }

  // Exibir métricas
  const metricas = [
    ['Total de Projetos:', totalProjetos],
    ['Orçamento Total:', `R$ ${formatarNumero(orcamentoTotal)}`],
    ['Progresso Médio:', `${progressoMedio}%`]
  ];

  aba.getRange(5, 1, metricas.length, 2).setValues(metricas);

  // Formatação
  aba.getRange(5, 1, metricas.length, 1).setFontWeight('bold');
  aba.getRange(5, 2, metricas.length, 1)
    .setHorizontalAlignment('right')
    .setNumberFormat('#,##0');
}

/**
 * Cria seção de distribuição por status
 */
function criarSecaoStatus(aba) {
  // Título da seção
  aba.getRange('D4').setValue('🎯 STATUS DOS PROJETOS');
  aba.getRange('D4:E4')
    .setFontWeight('bold')
    .setFontSize(14)
    .setBackground('#e6f3ff');

  // Obter dados de status
  const planilha = SpreadsheetApp.getActiveSpreadsheet();
  const abaDados = planilha.getSheetByName(CONFIG.ABA_DADOS);

  if (!abaDados) return;

  const dados = abaDados.getDataRange().getValues();
  const statusCount = {};

  // Contar status (assumindo coluna 4 = Status)
  for (let i = 1; i < dados.length; i++) {
    const status = dados[i][3] || 'Não Definido';
    statusCount[status] = (statusCount[status] || 0) + 1;
  }

  // Exibir contagem de status
  let linha = 5;
  Object.keys(statusCount).forEach(status => {
    aba.getRange(linha, 4).setValue(status + ':');
    aba.getRange(linha, 5).setValue(statusCount[status]);

    // Aplicar cor baseada no status
    if (CONFIG.CORES[status]) {
      aba.getRange(linha, 4, 1, 2).setBackground(CONFIG.CORES[status]);
      aba.getRange(linha, 4, 1, 2).setFontColor('#ffffff');
    }

    linha++;
  });

  aba.getRange(5, 4, Object.keys(statusCount).length, 1).setFontWeight('bold');
  aba.getRange(5, 5, Object.keys(statusCount).length, 1).setHorizontalAlignment('right');
}

/**
 * Cria seção de análise de orçamento
 */
function criarSecaoOrcamento(aba) {
  // Título da seção
  aba.getRange('A9').setValue('💰 ANÁLISE DE ORÇAMENTO');
  aba.getRange('A9:B9')
    .setFontWeight('bold')
    .setFontSize(14)
    .setBackground('#e6f3ff');

  // Calcular estatísticas de orçamento
  const planilha = SpreadsheetApp.getActiveSpreadsheet();
  const abaDados = planilha.getSheetByName(CONFIG.ABA_DADOS);

  if (!abaDados) return;

  const dados = abaDados.getDataRange().getValues();
  const orcamentos = [];

  // Coletar orçamentos (assumindo coluna 7 = Orçamento)
  for (let i = 1; i < dados.length; i++) {
    const orcamento = parseFloat(dados[i][6]) || 0;
    if (orcamento > 0) orcamentos.push(orcamento);
  }

  if (orcamentos.length > 0) {
    const total = orcamentos.reduce((sum, o) => sum + o, 0);
    const medio = total / orcamentos.length;
    const maximo = Math.max(...orcamentos);
    const minimo = Math.min(...orcamentos);

    const estatisticas = [
      ['Orçamento Total:', `R$ ${formatarNumero(total)}`],
      ['Orçamento Médio:', `R$ ${formatarNumero(medio)}`],
      ['Maior Orçamento:', `R$ ${formatarNumero(maximo)}`],
      ['Menor Orçamento:', `R$ ${formatarNumero(minimo)}`]
    ];

    aba.getRange(10, 1, estatisticas.length, 2).setValues(estatisticas);
    aba.getRange(10, 1, estatisticas.length, 1).setFontWeight('bold');
    aba.getRange(10, 2, estatisticas.length, 1).setHorizontalAlignment('right');
  }
}

/**
 * Cria seção de performance dos avaliadores
 */
function criarSecaoAvaliadores(aba) {
  // Título da seção
  aba.getRange('D9').setValue('👥 AVALIADORES');
  aba.getRange('D9:E9')
    .setFontWeight('bold')
    .setFontSize(14)
    .setBackground('#e6f3ff');

  // Contar projetos por avaliador
  const planilha = SpreadsheetApp.getActiveSpreadsheet();
  const abaDados = planilha.getSheetByName(CONFIG.ABA_DADOS);

  if (!abaDados) return;

  const dados = abaDados.getDataRange().getValues();
  const avaliadoresCount = {};

  // Contar projetos por avaliador (assumindo coluna 10 = Avaliador)
  for (let i = 1; i < dados.length; i++) {
    const avaliador = dados[i][9] || 'Não Atribuído';
    avaliadoresCount[avaliador] = (avaliadoresCount[avaliador] || 0) + 1;
  }

  // Exibir top avaliadores
  const avaliadoresOrdenados = Object.entries(avaliadoresCount)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 5);

  let linha = 10;
  avaliadoresOrdenados.forEach(([avaliador, count]) => {
    aba.getRange(linha, 4).setValue(avaliador + ':');
    aba.getRange(linha, 5).setValue(count);
    linha++;
  });

  aba.getRange(10, 4, avaliadoresOrdenados.length, 1).setFontWeight('bold');
  aba.getRange(10, 5, avaliadoresOrdenados.length, 1).setHorizontalAlignment('right');
}

/**
 * Cria seção de prazos críticos
 */
function criarSecaoPrazosCriticos(aba) {
  // Título da seção
  aba.getRange('A15').setValue('⚠️ PRAZOS CRÍTICOS (< 30 dias)');
  aba.getRange('A15:H15')
    .setFontWeight('bold')
    .setFontSize(14)
    .setBackground('#ffebee');

  // Cabeçalho da tabela
  const cabecalho = ['ID', 'Título', 'Responsável', 'Data Fim', 'Dias Restantes', 'Progresso', 'Status'];
  aba.getRange(16, 1, 1, cabecalho.length).setValues([cabecalho]);
  aba.getRange(16, 1, 1, cabecalho.length)
    .setFontWeight('bold')
    .setBackground('#f5f5f5');

  // Identificar projetos com prazos críticos
  const planilha = SpreadsheetApp.getActiveSpreadsheet();
  const abaDados = planilha.getSheetByName(CONFIG.ABA_DADOS);

  if (!abaDados) return;

  const dados = abaDados.getDataRange().getValues();
  const hoje = new Date();
  const prazosCriticos = [];

  for (let i = 1; i < dados.length; i++) {
    const linha = dados[i];
    const status = linha[3];
    const dataFim = new Date(linha[5]);

    if (status !== 'Concluído' && dataFim && !isNaN(dataFim)) {
      const diasRestantes = Math.ceil((dataFim - hoje) / (1000 * 60 * 60 * 24));

      if (diasRestantes <= 30) {
        prazosCriticos.push([
          linha[0], // ID
          linha[1], // Título
          linha[2], // Responsável
          dataFim.toLocaleDateString('pt-BR'),
          diasRestantes,
          linha[7] + '%', // Progresso
          linha[3] // Status
        ]);
      }
    }
  }

  // Ordenar por dias restantes
  prazosCriticos.sort((a, b) => a[4] - b[4]);

  // Exibir projetos críticos
  if (prazosCriticos.length > 0) {
    aba.getRange(17, 1, prazosCriticos.length, cabecalho.length).setValues(prazosCriticos);

    // Formatação condicional para dias restantes
    const rangeDias = aba.getRange(17, 5, prazosCriticos.length, 1);

    // Vermelho para < 7 dias
    const regraCritica = SpreadsheetApp.newConditionalFormatRule()
      .whenNumberLessThan(7)
      .setBackground('#ffcdd2')
      .setRanges([rangeDias])
      .build();

    // Amarelo para 7-15 dias
    const regraAlerta = SpreadsheetApp.newConditionalFormatRule()
      .whenNumberBetween(7, 15)
      .setBackground('#fff9c4')
      .setRanges([rangeDias])
      .build();

    aba.setConditionalFormatRules([regraCritica, regraAlerta]);
  } else {
    aba.getRange('A17').setValue('✅ Nenhum projeto com prazo crítico');
    aba.getRange('A17').setFontColor('#4caf50');
  }
}

/**
 * Aplica formatação final ao dashboard
 */
function aplicarFormatacaoFinal(aba) {
  // Ajustar altura das linhas
  aba.setRowHeight(1, 40);
  aba.setRowHeight(2, 25);

  // Bordas para seções
  const ranges = ['A4:B8', 'D4:E8', 'A9:B13', 'D9:E13', 'A15:H25'];
  ranges.forEach(range => {
    aba.getRange(range).setBorder(true, true, true, true, true, true);
  });

  // Congelar primeiras linhas
  aba.setFrozenRows(3);
}

/**
 * Formata números para exibição
 */
function formatarNumero(numero) {
  if (typeof numero !== 'number') return '0';

  return numero.toLocaleString('pt-BR', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  });
}

// ==================== CRIAÇÃO DE GRÁFICOS ====================
/**
 * Cria todos os gráficos do dashboard
 */
function criarGraficos() {
  try {
    console.log('📊 Criando gráficos...');

    const planilha = SpreadsheetApp.getActiveSpreadsheet();

    // Criar/obter aba de gráficos
    let abaGraficos = planilha.getSheetByName(CONFIG.ABA_GRAFICOS);
    if (!abaGraficos) {
      abaGraficos = planilha.insertSheet(CONFIG.ABA_GRAFICOS);
    }

    // Limpar gráficos existentes
    const graficos = abaGraficos.getCharts();
    graficos.forEach(grafico => abaGraficos.removeChart(grafico));
    abaGraficos.clear();

    // Preparar dados para gráficos
    prepararDadosGraficos(abaGraficos);

    // Criar gráficos específicos
    criarGraficoPizza(abaGraficos);
    criarGraficoBarras(abaGraficos);
    criarGraficoLinha(abaGraficos);
    criarGraficoOrcamento(abaGraficos);

    // Configurar layout da aba
    configurarLayoutGraficos(abaGraficos);

    console.log('✅ Gráficos criados com sucesso');

  } catch (error) {
    console.error('❌ Erro na criação de gráficos:', error);
    throw error;
  }
}

/**
 * Prepara dados estruturados para os gráficos
 */
function prepararDadosGraficos(aba) {
  const planilha = SpreadsheetApp.getActiveSpreadsheet();
  const abaDados = planilha.getSheetByName(CONFIG.ABA_DADOS);

  if (!abaDados) return;

  const dados = abaDados.getDataRange().getValues();

  // Título da aba
  aba.getRange('A1').setValue('📊 GRÁFICOS DO DASHBOARD');
  aba.getRange('A1')
    .setFontSize(16)
    .setFontWeight('bold')
    .setBackground('#1f4e79')
    .setFontColor('#ffffff');

  // ===== DADOS PARA GRÁFICO DE PIZZA (STATUS) =====
  aba.getRange('A3').setValue('Dados - Status dos Projetos');
  aba.getRange('A3').setFontWeight('bold');

  const statusCount = {};
  for (let i = 1; i < dados.length; i++) {
    const status = dados[i][3] || 'Não Definido';
    statusCount[status] = (statusCount[status] || 0) + 1;
  }

  const dadosStatus = [['Status', 'Quantidade']];
  Object.entries(statusCount).forEach(([status, count]) => {
    dadosStatus.push([status, count]);
  });

  aba.getRange(4, 1, dadosStatus.length, 2).setValues(dadosStatus);

  // ===== DADOS PARA GRÁFICO DE BARRAS (CATEGORIAS) =====
  const linhaCategoria = dadosStatus.length + 6;
  aba.getRange(linhaCategoria, 1).setValue('Dados - Projetos por Categoria');
  aba.getRange(linhaCategoria, 1).setFontWeight('bold');

  const categoriaCount = {};
  for (let i = 1; i < dados.length; i++) {
    const categoria = dados[i][8] || 'Não Definido';
    categoriaCount[categoria] = (categoriaCount[categoria] || 0) + 1;
  }

  const dadosCategoria = [['Categoria', 'Projetos']];
  Object.entries(categoriaCount).forEach(([categoria, count]) => {
    dadosCategoria.push([categoria, count]);
  });

  aba.getRange(linhaCategoria + 1, 1, dadosCategoria.length, 2).setValues(dadosCategoria);

  // ===== DADOS PARA GRÁFICO DE ORÇAMENTO =====
  const linhaOrcamento = linhaCategoria + dadosCategoria.length + 3;
  aba.getRange(linhaOrcamento, 1).setValue('Dados - Orçamento por Categoria');
  aba.getRange(linhaOrcamento, 1).setFontWeight('bold');

  const orcamentoCategoria = {};
  for (let i = 1; i < dados.length; i++) {
    const categoria = dados[i][8] || 'Não Definido';
    const orcamento = parseFloat(dados[i][6]) || 0;
    orcamentoCategoria[categoria] = (orcamentoCategoria[categoria] || 0) + orcamento;
  }

  const dadosOrcamento = [['Categoria', 'Orçamento (R$)']];
  Object.entries(orcamentoCategoria).forEach(([categoria, orcamento]) => {
    dadosOrcamento.push([categoria, orcamento]);
  });

  aba.getRange(linhaOrcamento + 1, 1, dadosOrcamento.length, 2).setValues(dadosOrcamento);

  // ===== DADOS PARA GRÁFICO DE LINHA (TENDÊNCIA TEMPORAL) =====
  const linhaTempo = linhaOrcamento + dadosOrcamento.length + 3;
  aba.getRange(linhaTempo, 1).setValue('Dados - Projetos por Mês');
  aba.getRange(linhaTempo, 1).setFontWeight('bold');

  const projetosPorMes = {};
  for (let i = 1; i < dados.length; i++) {
    const dataInicio = new Date(dados[i][4]);
    if (dataInicio && !isNaN(dataInicio)) {
      const mes = `${dataInicio.getFullYear()}-${(dataInicio.getMonth() + 1).toString().padStart(2, '0')}`;
      projetosPorMes[mes] = (projetosPorMes[mes] || 0) + 1;
    }
  }

  const dadosTempo = [['Mês', 'Novos Projetos']];
  Object.entries(projetosPorMes)
    .sort((a, b) => a[0].localeCompare(b[0]))
    .forEach(([mes, count]) => {
      dadosTempo.push([mes, count]);
    });

  aba.getRange(linhaTempo + 1, 1, dadosTempo.length, 2).setValues(dadosTempo);

  // Armazenar posições para referência
  aba.getRange('Z1').setValue(JSON.stringify({
    statusRange: `A4:B${3 + dadosStatus.length}`,
    categoriaRange: `A${linhaCategoria + 1}:B${linhaCategoria + dadosCategoria.length}`,
    orcamentoRange: `A${linhaOrcamento + 1}:B${linhaOrcamento + dadosOrcamento.length}`,
    tempoRange: `A${linhaTempo + 1}:B${linhaTempo + dadosTempo.length}`
  }));
}

/**
 * Cria gráfico de pizza para distribuição de status
 */
function criarGraficoPizza(aba) {
  try {
    const ranges = JSON.parse(aba.getRange('Z1').getValue());
    const dataRange = aba.getRange(ranges.statusRange);

    const grafico = aba.newChart()
      .setChartType(Charts.ChartType.PIE)
      .addRange(dataRange)
      .setPosition(2, 4, 0, 0)
      .setOption('title', 'Distribuição por Status')
      .setOption('titleTextStyle', {
        fontSize: 14,
        bold: true
      })
      .setOption('pieSliceTextStyle', {
        fontSize: 10
      })
      .setOption('legend', {
        position: 'right',
        textStyle: { fontSize: 10 }
      })
      .setOption('width', 400)
      .setOption('height', 300)
      .setOption('colors', Object.values(CONFIG.CORES))
      .build();

    aba.insertChart(grafico);

  } catch (error) {
    console.error('Erro no gráfico de pizza:', error);
  }
}

/**
 * Cria gráfico de barras para categorias
 */
function criarGraficoBarras(aba) {
  try {
    const ranges = JSON.parse(aba.getRange('Z1').getValue());
    const dataRange = aba.getRange(ranges.categoriaRange);

    const grafico = aba.newChart()
      .setChartType(Charts.ChartType.COLUMN)
      .addRange(dataRange)
      .setPosition(2, 8, 0, 0)
      .setOption('title', 'Projetos por Categoria')
      .setOption('titleTextStyle', {
        fontSize: 14,
        bold: true
      })
      .setOption('hAxis', {
        title: 'Categoria',
        titleTextStyle: { fontSize: 12 },
        textStyle: { fontSize: 9 }
      })
      .setOption('vAxis', {
        title: 'Número de Projetos',
        titleTextStyle: { fontSize: 12 }
      })
      .setOption('legend', { position: 'none' })
      .setOption('width', 400)
      .setOption('height', 300)
      .setOption('colors', ['#2196F3'])
      .build();

    aba.insertChart(grafico);

  } catch (error) {
    console.error('Erro no gráfico de barras:', error);
  }
}

/**
 * Cria gráfico de linha para tendência temporal
 */
function criarGraficoLinha(aba) {
  try {
    const ranges = JSON.parse(aba.getRange('Z1').getValue());
    const dataRange = aba.getRange(ranges.tempoRange);

    const grafico = aba.newChart()
      .setChartType(Charts.ChartType.LINE)
      .addRange(dataRange)
      .setPosition(18, 4, 0, 0)
      .setOption('title', 'Tendência de Novos Projetos')
      .setOption('titleTextStyle', {
        fontSize: 14,
        bold: true
      })
      .setOption('hAxis', {
        title: 'Período',
        titleTextStyle: { fontSize: 12 },
        textStyle: { fontSize: 9 }
      })
      .setOption('vAxis', {
        title: 'Novos Projetos',
        titleTextStyle: { fontSize: 12 }
      })
      .setOption('legend', { position: 'none' })
      .setOption('width', 400)
      .setOption('height', 300)
      .setOption('colors', ['#FF9800'])
      .setOption('curveType', 'function')
      .setOption('pointSize', 5)
      .build();

    aba.insertChart(grafico);

  } catch (error) {
    console.error('Erro no gráfico de linha:', error);
  }
}

/**
 * Cria gráfico de orçamento por categoria
 */
function criarGraficoOrcamento(aba) {
  try {
    const ranges = JSON.parse(aba.getRange('Z1').getValue());
    const dataRange = aba.getRange(ranges.orcamentoRange);

    const grafico = aba.newChart()
      .setChartType(Charts.ChartType.BAR)
      .addRange(dataRange)
      .setPosition(18, 8, 0, 0)
      .setOption('title', 'Orçamento por Categoria')
      .setOption('titleTextStyle', {
        fontSize: 14,
        bold: true
      })
      .setOption('hAxis', {
        title: 'Orçamento (R$)',
        titleTextStyle: { fontSize: 12 },
        format: 'currency'
      })
      .setOption('vAxis', {
        title: 'Categoria',
        titleTextStyle: { fontSize: 12 },
        textStyle: { fontSize: 9 }
      })
      .setOption('legend', { position: 'none' })
      .setOption('width', 400)
      .setOption('height', 300)
      .setOption('colors', ['#4CAF50'])
      .build();

    aba.insertChart(grafico);

  } catch (error) {
    console.error('Erro no gráfico de orçamento:', error);
  }
}

/**
 * Configura o layout da aba de gráficos
 */
function configurarLayoutGraficos(aba) {
  // Ajustar larguras das colunas
  aba.setColumnWidth(1, 150);
  aba.setColumnWidth(2, 120);
  aba.setColumnWidth(3, 50);

  // Formatar cabeçalhos
  const cabecalhos = aba.getRange('A1:Z1');
  cabecalhos.setBackground('#1f4e79').setFontColor('#ffffff');

  // Ocultar coluna de dados auxiliares
  aba.hideColumns(26); // Coluna Z

  // Congelar primeira linha
  aba.setFrozenRows(1);
}

// ==================== ABA DE MÉTRICAS ====================
/**
 * Cria a aba de métricas com dados estruturados
 */
function criarAbaMetricas(planilha, metricas) {
  try {
    console.log('📋 Criando aba de métricas...');

    // Criar/obter aba de métricas
    let abaMetricas = planilha.getSheetByName(CONFIG.ABA_METRICAS);
    if (!abaMetricas) {
      abaMetricas = planilha.insertSheet(CONFIG.ABA_METRICAS);
    }

    // Limpar conteúdo existente
    abaMetricas.clear();

    // Configurar layout
    configurarLayoutMetricas(abaMetricas);

    // Inserir seções de métricas
    let linhaAtual = 3;

    linhaAtual = inserirResumoGeral(abaMetricas, metricas.resumoGeral, linhaAtual);
    linhaAtual = inserirDistribuicaoStatus(abaMetricas, metricas.statusDistribuicao, linhaAtual);
    linhaAtual = inserirAnaliseOrcamento(abaMetricas, metricas.orcamentoAnalise, linhaAtual);
    linhaAtual = inserirPerformanceAvaliadores(abaMetricas, metricas.avaliadoresPerformance, linhaAtual);
    linhaAtual = inserirDistribuicaoCategoria(abaMetricas, metricas.categoriaDistribuicao, linhaAtual);
    linhaAtual = inserirPrazosCriticos(abaMetricas, metricas.prazosCriticos, linhaAtual);
    linhaAtual = inserirTendencias(abaMetricas, metricas.tendencias, linhaAtual);

    // Aplicar formatação final
    aplicarFormatacaoMetricas(abaMetricas);

    console.log('✅ Aba de métricas criada com sucesso');

  } catch (error) {
    console.error('❌ Erro na criação da aba de métricas:', error);
    throw error;
  }
}

/**
 * Configura o layout básico da aba de métricas
 */
function configurarLayoutMetricas(aba) {
  // Definir larguras das colunas
  aba.setColumnWidth(1, 250);  // Coluna A - Descrição
  aba.setColumnWidth(2, 150);  // Coluna B - Valor
  aba.setColumnWidth(3, 100);  // Coluna C - Unidade/Tipo
  aba.setColumnWidth(4, 50);   // Coluna D - Espaçador
  aba.setColumnWidth(5, 200);  // Coluna E - Descrição adicional
  aba.setColumnWidth(6, 150);  // Coluna F - Valor adicional

  // Título principal
  aba.getRange('A1:F1').merge();
  aba.getRange('A1').setValue('📊 MÉTRICAS DETALHADAS DO PORTFÓLIO');
  aba.getRange('A1')
    .setFontSize(18)
    .setFontWeight('bold')
    .setHorizontalAlignment('center')
    .setBackground('#1f4e79')
    .setFontColor('#ffffff');

  // Data de cálculo
  aba.getRange('A2:F2').merge();
  aba.getRange('A2').setValue(`Calculado em: ${new Date().toLocaleString('pt-BR')}`);
  aba.getRange('A2')
    .setFontSize(10)
    .setHorizontalAlignment('center')
    .setBackground('#f0f0f0')
    .setFontStyle('italic');
}

/**
 * Insere resumo geral
 */
function inserirResumoGeral(aba, resumo, linha) {
  // Título da seção
  aba.getRange(linha, 1).setValue('📈 RESUMO GERAL');
  aba.getRange(linha, 1, 1, 3)
    .setFontWeight('bold')
    .setFontSize(14)
    .setBackground('#e3f2fd');

  linha++;

  // Dados do resumo
  const dados = [
    ['Total de Projetos', resumo.totalProjetos, 'projetos'],
    ['Orçamento Total', resumo.orcamentoTotal, 'R$'],
    ['Progresso Médio', resumo.progressoMedio, '%']
  ];

  aba.getRange(linha, 1, dados.length, 3).setValues(dados);

  // Formatação
  aba.getRange(linha, 1, dados.length, 1).setFontWeight('bold');
  aba.getRange(linha, 2, dados.length, 1)
    .setHorizontalAlignment('right')
    .setNumberFormat('#,##0.00');

  return linha + dados.length + 2;
}

/**
 * Insere distribuição por status
 */
function inserirDistribuicaoStatus(aba, distribuicao, linha) {
  // Título da seção
  aba.getRange(linha, 1).setValue('🎯 DISTRIBUIÇÃO POR STATUS');
  aba.getRange(linha, 1, 1, 3)
    .setFontWeight('bold')
    .setFontSize(14)
    .setBackground('#e8f5e8');

  linha++;

  // Cabeçalho
  const cabecalho = [['Status', 'Quantidade', 'Percentual']];
  aba.getRange(linha, 1, 1, 3).setValues(cabecalho);
  aba.getRange(linha, 1, 1, 3)
    .setFontWeight('bold')
    .setBackground('#f5f5f5');

  linha++;

  // Dados da distribuição
  const dados = [];
  Object.entries(distribuicao).forEach(([status, info]) => {
    dados.push([status, info.quantidade, info.percentual + '%']);
  });

  if (dados.length > 0) {
    aba.getRange(linha, 1, dados.length, 3).setValues(dados);

    // Aplicar cores por status
    for (let i = 0; i < dados.length; i++) {
      const status = dados[i][0];
      if (CONFIG.CORES[status]) {
        aba.getRange(linha + i, 1, 1, 3)
          .setBackground(CONFIG.CORES[status])
          .setFontColor('#ffffff');
      }
    }
  }

  return linha + dados.length + 2;
}

/**
 * Insere análise de orçamento
 */
function inserirAnaliseOrcamento(aba, analise, linha) {
  // Título da seção
  aba.getRange(linha, 1).setValue('💰 ANÁLISE DE ORÇAMENTO');
  aba.getRange(linha, 1, 1, 3)
    .setFontWeight('bold')
    .setFontSize(14)
    .setBackground('#fff3e0');

  linha++;

  // Dados da análise
  const dados = [
    ['Orçamento Total', analise.total, 'R$'],
    ['Orçamento Médio', analise.medio, 'R$'],
    ['Maior Orçamento', analise.maximo, 'R$'],
    ['Menor Orçamento', analise.minimo, 'R$']
  ];

  aba.getRange(linha, 1, dados.length, 3).setValues(dados);

  // Formatação
  aba.getRange(linha, 1, dados.length, 1).setFontWeight('bold');
  aba.getRange(linha, 2, dados.length, 1)
    .setHorizontalAlignment('right')
    .setNumberFormat('#,##0');

  return linha + dados.length + 2;
}

/**
 * Insere performance dos avaliadores
 */
function inserirPerformanceAvaliadores(aba, performance, linha) {
  // Título da seção
  aba.getRange(linha, 1).setValue('👥 PERFORMANCE DOS AVALIADORES');
  aba.getRange(linha, 1, 1, 6)
    .setFontWeight('bold')
    .setFontSize(14)
    .setBackground('#f3e5f5');

  linha++;

  // Cabeçalho
  const cabecalho = [['Avaliador', 'Total Proj.', 'Concluídos', 'Em Andamento', 'Taxa Conclusão', 'Orçamento Total']];
  aba.getRange(linha, 1, 1, 6).setValues(cabecalho);
  aba.getRange(linha, 1, 1, 6)
    .setFontWeight('bold')
    .setBackground('#f5f5f5');

  linha++;

  // Dados dos avaliadores
  const dados = [];
  Object.entries(performance).forEach(([avaliador, perf]) => {
    dados.push([
      avaliador,
      perf.totalProjetos,
      perf.concluidos,
      perf.emAndamento,
      perf.taxaConclusao + '%',
      'R$ ' + perf.orcamentoTotal.toLocaleString('pt-BR')
    ]);
  });

  if (dados.length > 0) {
    aba.getRange(linha, 1, dados.length, 6).setValues(dados);

    // Formatação
    aba.getRange(linha, 2, dados.length, 3).setHorizontalAlignment('center');
    aba.getRange(linha, 5, dados.length, 1).setHorizontalAlignment('center');
    aba.getRange(linha, 6, dados.length, 1).setHorizontalAlignment('right');
  }

  return linha + dados.length + 2;
}

/**
 * Insere distribuição por categoria
 */
function inserirDistribuicaoCategoria(aba, distribuicao, linha) {
  // Título da seção
  aba.getRange(linha, 1).setValue('📂 DISTRIBUIÇÃO POR CATEGORIA');
  aba.getRange(linha, 1, 1, 4)
    .setFontWeight('bold')
    .setFontSize(14)
    .setBackground('#e0f2f1');

  linha++;

  // Cabeçalho
  const cabecalho = [['Categoria', 'Quantidade', 'Orçamento Total', 'Progresso Médio']];
  aba.getRange(linha, 1, 1, 4).setValues(cabecalho);
  aba.getRange(linha, 1, 1, 4)
    .setFontWeight('bold')
    .setBackground('#f5f5f5');

  linha++;

  // Dados das categorias
  const dados = [];
  Object.entries(distribuicao).forEach(([categoria, info]) => {
    dados.push([
      categoria,
      info.quantidade,
      'R$ ' + info.orcamentoTotal.toLocaleString('pt-BR'),
      info.progressoMedio + '%'
    ]);
  });

  if (dados.length > 0) {
    aba.getRange(linha, 1, dados.length, 4).setValues(dados);

    // Formatação
    aba.getRange(linha, 2, dados.length, 1).setHorizontalAlignment('center');
    aba.getRange(linha, 3, dados.length, 1).setHorizontalAlignment('right');
    aba.getRange(linha, 4, dados.length, 1).setHorizontalAlignment('center');
  }

  return linha + dados.length + 2;
}

/**
 * Insere projetos com prazos críticos
 */
function inserirPrazosCriticos(aba, prazosCriticos, linha) {
  // Título da seção
  aba.getRange(linha, 1).setValue('⚠️ PROJETOS COM PRAZOS CRÍTICOS');
  aba.getRange(linha, 1, 1, 6)
    .setFontWeight('bold')
    .setFontSize(14)
    .setBackground('#ffebee');

  linha++;

  if (prazosCriticos.length === 0) {
    aba.getRange(linha, 1).setValue('✅ Nenhum projeto com prazo crítico');
    aba.getRange(linha, 1).setFontColor('#4caf50');
    return linha + 2;
  }

  // Cabeçalho
  const cabecalho = [['ID', 'Título', 'Responsável', 'Data Fim', 'Dias Restantes', 'Progresso']];
  aba.getRange(linha, 1, 1, 6).setValues(cabecalho);
  aba.getRange(linha, 1, 1, 6)
    .setFontWeight('bold')
    .setBackground('#f5f5f5');

  linha++;

  // Dados dos projetos críticos
  const dados = [];
  prazosCriticos.forEach(projeto => {
    dados.push([
      projeto.id,
      projeto.titulo,
      projeto.responsavel,
      projeto.dataFim.toLocaleDateString('pt-BR'),
      projeto.diasRestantes,
      projeto.progresso + '%'
    ]);
  });

  aba.getRange(linha, 1, dados.length, 6).setValues(dados);

  // Formatação condicional para dias restantes
  const rangeDias = aba.getRange(linha, 5, dados.length, 1);

  // Vermelho para < 7 dias
  const regraCritica = SpreadsheetApp.newConditionalFormatRule()
    .whenNumberLessThan(7)
    .setBackground('#ffcdd2')
    .setRanges([rangeDias])
    .build();

  // Amarelo para 7-15 dias
  const regraAlerta = SpreadsheetApp.newConditionalFormatRule()
    .whenNumberBetween(7, 15)
    .setBackground('#fff9c4')
    .setRanges([rangeDias])
    .build();

  aba.setConditionalFormatRules([regraCritica, regraAlerta]);

  return linha + dados.length + 2;
}

/**
 * Insere análise de tendências
 */
function inserirTendencias(aba, tendencias, linha) {
  // Título da seção
  aba.getRange(linha, 1).setValue('📈 TENDÊNCIAS TEMPORAIS');
  aba.getRange(linha, 1, 1, 6)
    .setFontWeight('bold')
    .setFontSize(14)
    .setBackground('#e1f5fe');

  linha++;

  // Projetos por mês
  aba.getRange(linha, 1).setValue('Novos Projetos por Mês:');
  aba.getRange(linha, 1).setFontWeight('bold');
  linha++;

  const mesesOrdenados = Object.keys(tendencias.projetosPorMes).sort();
  mesesOrdenados.forEach(mes => {
    aba.getRange(linha, 1).setValue(mes);
    aba.getRange(linha, 2).setValue(tendencias.projetosPorMes[mes]);
    linha++;
  });

  return linha + 2;
}

/**
 * Aplica formatação final à aba de métricas
 */
function aplicarFormatacaoMetricas(aba) {
  // Ajustar altura das linhas
  aba.setRowHeight(1, 40);
  aba.setRowHeight(2, 25);

  // Congelar primeiras linhas
  aba.setFrozenRows(2);

  // Bordas para toda a área de dados
  const ultimaLinha = aba.getLastRow();
  const ultimaColuna = aba.getLastColumn();

  if (ultimaLinha > 2 && ultimaColuna > 0) {
    aba.getRange(3, 1, ultimaLinha - 2, ultimaColuna)
      .setBorder(true, true, true, true, true, true);
  }
}

// ==================== MENU E TRIGGERS ====================
/**
 * Cria menu personalizado na planilha
 */
function criarMenuCustomizado() {
  const ui = SpreadsheetApp.getUi();

  ui.createMenu('📊 Dashboard Projetos')
    .addItem('🔄 Atualizar Dados', 'atualizarDashboard')
    .addItem('📈 Recalcular Métricas', 'calcularMetricas')
    .addItem('📊 Recriar Gráficos', 'criarGraficos')
    .addSeparator()
    .addItem('📁 Reimportar Arquivo', 'importarDadosDoArquivo')
    .addItem('📧 Enviar Relatório', 'enviarRelatorio')
    .addSeparator()
    .addItem('⚙️ Configurações', 'abrirConfiguracoes')
    .addItem('❓ Ajuda', 'mostrarAjuda')
    .addToUi();
}

/**
 * Configura triggers para atualização automática
 */
function configurarTriggers() {
  // Remover triggers existentes
  const triggers = ScriptApp.getProjectTriggers();
  triggers.forEach(trigger => {
    if (trigger.getHandlerFunction() === 'atualizarDashboardAutomatico') {
      ScriptApp.deleteTrigger(trigger);
    }
  });

  // Criar trigger para atualização diária
  ScriptApp.newTrigger('atualizarDashboardAutomatico')
    .timeBased()
    .everyDays(1)
    .atHour(8)
    .create();
}

/**
 * Executada quando a planilha é aberta
 */
function onOpen() {
  criarMenuCustomizado();

  // Verificar se o dashboard está configurado
  const planilha = SpreadsheetApp.getActiveSpreadsheet();
  const aba = planilha.getSheetByName(CONFIG.ABA_DASHBOARD);

  if (!aba) {
    SpreadsheetApp.getUi().alert(
      '👋 Bem-vindo ao Dashboard de Projetos!\n\n' +
      'Para começar, execute:\n' +
      'Menu "📊 Dashboard Projetos" → "🔄 Atualizar Dados"\n\n' +
      'Certifique-se de que o arquivo data.txt está no Google Drive.'
    );
  }
}

/**
 * Executada quando há edição na planilha
 */
function onEdit(e) {
  const aba = e.source.getActiveSheet();

  // Se editou a aba de dados, recalcular métricas
  if (aba.getName() === CONFIG.ABA_DADOS) {
    Utilities.sleep(1000); // Aguardar 1 segundo
    calcularMetricas();
  }
}

/**
 * Atualização automática diária
 */
function atualizarDashboardAutomatico() {
  try {
    atualizarDashboard();
  } catch (error) {
    console.error('Erro na atualização automática:', error);
  }
}

/**
 * Atualiza todo o dashboard
 */
function atualizarDashboard() {
  try {
    importarDadosDoArquivo();
    calcularMetricas();
    criarDashboardVisual();

    SpreadsheetApp.getUi().alert('✅ Dashboard atualizado com sucesso!');
  } catch (error) {
    SpreadsheetApp.getUi().alert('❌ Erro na atualização: ' + error.message);
  }
}

/**
 * Mostra ajuda para o usuário
 */
function mostrarAjuda() {
  const ajuda = `
📊 DASHBOARD DE PROJETOS - AJUDA

🔧 CONFIGURAÇÃO INICIAL:
1. Faça upload do arquivo data.txt para o Google Drive
2. Execute: Menu → "🔄 Atualizar Dados"

📁 FORMATO DO ARQUIVO data.txt:
- Separador: | (pipe)
- Primeira linha: cabeçalhos
- Demais linhas: dados dos projetos

🎯 FUNCIONALIDADES:
• Importação automática de dados
• Cálculo de métricas em tempo real
• Gráficos interativos
• Relatórios automáticos
• Atualização diária

❓ PROBLEMAS COMUNS:
• Arquivo não encontrado: Verifique se data.txt está no Drive
• Dados incorretos: Verifique formato do arquivo
• Gráficos não aparecem: Execute "📊 Recriar Gráficos"

📧 SUPORTE: Verifique os logs no Apps Script Editor
  `;

  SpreadsheetApp.getUi().alert(ajuda);
}

// ==================== CONFIGURAÇÕES AVANÇADAS ====================
/**
 * Abre interface de configurações
 */
function abrirConfiguracoes() {
  SpreadsheetApp.getUi().alert(
    '⚙️ CONFIGURAÇÕES\n\n' +
    'Configurações disponíveis:\n\n' +
    '📁 Arquivo de dados: data.txt\n' +
    '🎨 Cores: Configuradas no código\n' +
    '⏰ Atualização: Diária às 8h\n' +
    '📧 Relatórios: Configurar manualmente\n\n' +
    'Para configurações avançadas, edite o código no Apps Script.'
  );
}

/**
 * Envia relatório por email
 */
function enviarRelatorio() {
  try {
    const email = SpreadsheetApp.getUi().prompt(
      '📧 Enviar Relatório',
      'Digite o email para envio do relatório:',
      SpreadsheetApp.getUi().ButtonSet.OK_CANCEL
    );

    if (email.getSelectedButton() === SpreadsheetApp.getUi().Button.OK) {
      const emailDestino = email.getResponseText();

      if (emailDestino) {
        // Gerar relatório
        const relatorio = gerarRelatorioTexto();

        // Enviar email
        MailApp.sendEmail({
          to: emailDestino,
          subject: `📊 Relatório Dashboard Projetos - ${new Date().toLocaleDateString('pt-BR')}`,
          body: relatorio
        });

        SpreadsheetApp.getUi().alert('✅ Relatório enviado com sucesso!');
      }
    }

  } catch (error) {
    SpreadsheetApp.getUi().alert('❌ Erro no envio: ' + error.message);
  }
}

/**
 * Gera relatório em texto
 */
function gerarRelatorioTexto() {
  const planilha = SpreadsheetApp.getActiveSpreadsheet();
  const abaDados = planilha.getSheetByName(CONFIG.ABA_DADOS);

  if (!abaDados) return 'Dados não encontrados';

  const dados = abaDados.getDataRange().getValues();

  // Calcular estatísticas básicas
  const total = dados.length - 1;
  const statusCount = {};

  for (let i = 1; i < dados.length; i++) {
    const status = dados[i][3] || 'Não Definido';
    statusCount[status] = (statusCount[status] || 0) + 1;
  }

  let relatorio = `RELATÓRIO DASHBOARD PROJETOS
Data: ${new Date().toLocaleString('pt-BR')}

RESUMO GERAL:
- Total de Projetos: ${total}

DISTRIBUIÇÃO POR STATUS:
`;

  Object.entries(statusCount).forEach(([status, count]) => {
    const percentual = ((count / total) * 100).toFixed(1);
    relatorio += `- ${status}: ${count} (${percentual}%)\n`;
  });

  return relatorio;
}

// ==================== FUNÇÕES AUXILIARES ====================
/**
 * Cria estrutura básica da planilha
 */
function criarEstruturaPlanilha() {
  const planilha = SpreadsheetApp.getActiveSpreadsheet();

  // Criar abas necessárias se não existirem
  const abasNecessarias = [
    CONFIG.ABA_DADOS,
    CONFIG.ABA_DASHBOARD,
    CONFIG.ABA_METRICAS,
    CONFIG.ABA_GRAFICOS
  ];

  abasNecessarias.forEach(nomeAba => {
    let aba = planilha.getSheetByName(nomeAba);
    if (!aba) {
      aba = planilha.insertSheet(nomeAba);
      console.log(`✅ Aba "${nomeAba}" criada`);
    }
  });

  // Remover aba padrão se existir e estiver vazia
  try {
    const abaPadrao = planilha.getSheetByName('Planilha1');
    if (abaPadrao && abaPadrao.getLastRow() <= 1) {
      planilha.deleteSheet(abaPadrao);
    }
  } catch (error) {
    // Ignorar erro se aba não existir
  }
}

/**
 * Cria backup dos dados
 */
function criarBackup() {
  try {
    const planilha = SpreadsheetApp.getActiveSpreadsheet();
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const nomeBackup = `Backup_Dashboard_${timestamp}`;

    // Criar cópia da planilha
    const backup = planilha.copy(nomeBackup);

    console.log(`✅ Backup criado: ${nomeBackup}`);
    return nomeBackup;

  } catch (error) {
    console.error('❌ Erro no backup:', error);
    throw error;
  }
}

// ==================== FIM DO CÓDIGO ====================
/**
 * 🎉 DASHBOARD COMPLETO IMPLEMENTADO!
 *
 * INSTRUÇÕES DE USO:
 * 1. Faça upload do arquivo data.txt para o Google Drive
 * 2. Execute a função inicializarDashboard()
 * 3. Autorize as permissões necessárias
 * 4. Aproveite seu dashboard profissional!
 *
 * FUNCIONALIDADES INCLUÍDAS:
 * ✅ Importação automática de dados
 * ✅ Dashboard visual completo
 * ✅ Gráficos interativos
 * ✅ Métricas detalhadas
 * ✅ Menu personalizado
 * ✅ Atualização automática
 * ✅ Relatórios por email
 * ✅ Sistema de backup
 *
 * Desenvolvido para FACEPE - 2025
 */
